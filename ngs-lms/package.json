{"name": "ngs-lms", "version": "1.0.0", "description": "NGS Learning Management System", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "docker:build": "docker-compose build", "migrate": "cd backend && npm run migrate", "seed": "cd backend && npm run seed"}, "keywords": ["ngs", "bioinformatics", "lms", "education", "genomics"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0"}, "workspaces": ["frontend", "backend"]}