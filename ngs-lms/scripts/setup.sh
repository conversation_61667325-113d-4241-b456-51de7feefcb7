#!/bin/bash

# NGS LMS Setup Script
# This script sets up the development environment for NGS LMS

set -e

echo "🚀 Setting up NGS LMS Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Node.js $(node --version) is installed"
}

# Create environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_success "Created backend/.env from example"
    else
        print_warning "backend/.env already exists, skipping..."
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        cp frontend/.env.example frontend/.env
        print_success "Created frontend/.env from example"
    else
        print_warning "frontend/.env already exists, skipping..."
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    if [ -f "package.json" ]; then
        npm install
        print_success "Installed root dependencies"
    fi
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    print_success "Installed backend dependencies"
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    print_success "Installed frontend dependencies"
}

# Build practice environment Docker image
build_practice_env() {
    print_status "Building NGS practice environment Docker image..."
    docker build -t ngs-lms/practice-env:latest docker/practice-env/
    print_success "Built NGS practice environment image"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Start database services
    docker-compose -f docker-compose.dev.yml up -d postgres redis minio
    
    # Wait for database to be ready
    print_status "Waiting for database to be ready..."
    sleep 10
    
    # Run database migrations
    cd backend
    npx prisma migrate dev --name init
    print_success "Database migrations completed"
    
    # Generate Prisma client
    npx prisma generate
    print_success "Prisma client generated"
    
    # Seed database
    npx prisma db seed
    print_success "Database seeded with initial data"
    
    cd ..
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p uploads
    mkdir -p logs
    mkdir -p backend/logs
    mkdir -p frontend/build
    
    print_success "Created necessary directories"
}

# Set permissions
set_permissions() {
    print_status "Setting permissions..."
    
    chmod +x scripts/*.sh
    chmod +x docker/practice-env/scripts/*.sh
    
    print_success "Set script permissions"
}

# Main setup function
main() {
    echo "🔧 NGS LMS Development Environment Setup"
    echo "========================================"
    
    check_docker
    check_nodejs
    create_directories
    setup_env_files
    install_dependencies
    set_permissions
    build_practice_env
    setup_database
    
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Start the development environment:"
    echo "   npm run docker:dev"
    echo ""
    echo "2. Or start services individually:"
    echo "   npm run dev:backend    # Start backend server"
    echo "   npm run dev:frontend   # Start frontend server"
    echo ""
    echo "3. Access the application:"
    echo "   Frontend: http://localhost:3001"
    echo "   Backend API: http://localhost:3000"
    echo "   API Documentation: http://localhost:3000/api-docs"
    echo "   Database Admin: http://localhost:8080 (with --profile tools)"
    echo ""
    echo "4. Default login credentials:"
    echo "   Admin: <EMAIL> / admin123!@#"
    echo "   Instructor: <EMAIL> / instructor123!@#"
    echo "   Student: <EMAIL> / student123!@#"
    echo ""
    echo "📚 For more information, check the README.md file"
}

# Run main function
main "$@"
