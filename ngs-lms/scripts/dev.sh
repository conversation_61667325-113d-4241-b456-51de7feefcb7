#!/bin/bash

# NGS LMS Development Script
# This script provides convenient commands for development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show help
show_help() {
    echo "NGS LMS Development Helper"
    echo "========================="
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start         Start all services with Docker Compose"
    echo "  stop          Stop all services"
    echo "  restart       Restart all services"
    echo "  logs          Show logs from all services"
    echo "  logs [service] Show logs from specific service"
    echo "  build         Build all Docker images"
    echo "  clean         Clean up containers and volumes"
    echo "  db:reset      Reset database (drop and recreate)"
    echo "  db:seed       Seed database with sample data"
    echo "  db:migrate    Run database migrations"
    echo "  test          Run tests"
    echo "  lint          Run linting"
    echo "  format        Format code"
    echo "  status        Show service status"
    echo "  shell [service] Open shell in service container"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                 # Start all services"
    echo "  $0 logs backend          # Show backend logs"
    echo "  $0 shell backend         # Open shell in backend container"
    echo "  $0 db:reset              # Reset database"
}

# Start services
start_services() {
    print_status "Starting NGS LMS development environment..."
    docker-compose -f docker-compose.dev.yml up -d
    print_success "All services started"
    
    echo ""
    echo "🌐 Service URLs:"
    echo "   Frontend: http://localhost:3001"
    echo "   Backend API: http://localhost:3000"
    echo "   API Docs: http://localhost:3000/api-docs"
    echo "   Database Admin: http://localhost:8080"
    echo "   Redis Commander: http://localhost:8081"
    echo "   MinIO Console: http://localhost:9001"
}

# Stop services
stop_services() {
    print_status "Stopping NGS LMS services..."
    docker-compose -f docker-compose.dev.yml down
    print_success "All services stopped"
}

# Restart services
restart_services() {
    print_status "Restarting NGS LMS services..."
    docker-compose -f docker-compose.dev.yml restart
    print_success "All services restarted"
}

# Show logs
show_logs() {
    if [ -z "$1" ]; then
        docker-compose -f docker-compose.dev.yml logs -f
    else
        docker-compose -f docker-compose.dev.yml logs -f "$1"
    fi
}

# Build images
build_images() {
    print_status "Building Docker images..."
    docker-compose -f docker-compose.dev.yml build
    
    # Build practice environment
    print_status "Building NGS practice environment..."
    docker build -t ngs-lms/practice-env:latest docker/practice-env/
    
    print_success "All images built successfully"
}

# Clean up
clean_up() {
    print_warning "This will remove all containers, networks, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up..."
        docker-compose -f docker-compose.dev.yml down -v --remove-orphans
        docker system prune -f
        print_success "Cleanup completed"
    else
        print_status "Cleanup cancelled"
    fi
}

# Reset database
reset_database() {
    print_warning "This will delete all data in the database!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Resetting database..."
        
        # Stop backend to avoid connection issues
        docker-compose -f docker-compose.dev.yml stop backend
        
        # Reset database
        docker-compose -f docker-compose.dev.yml exec postgres psql -U ngs_user -d postgres -c "DROP DATABASE IF EXISTS ngs_lms_dev;"
        docker-compose -f docker-compose.dev.yml exec postgres psql -U ngs_user -d postgres -c "CREATE DATABASE ngs_lms_dev;"
        
        # Run migrations and seed
        cd backend
        npx prisma migrate reset --force
        cd ..
        
        # Restart backend
        docker-compose -f docker-compose.dev.yml start backend
        
        print_success "Database reset completed"
    else
        print_status "Database reset cancelled"
    fi
}

# Seed database
seed_database() {
    print_status "Seeding database..."
    cd backend
    npx prisma db seed
    cd ..
    print_success "Database seeded"
}

# Run migrations
run_migrations() {
    print_status "Running database migrations..."
    cd backend
    npx prisma migrate dev
    cd ..
    print_success "Migrations completed"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    # Backend tests
    print_status "Running backend tests..."
    cd backend
    npm test
    cd ..
    
    # Frontend tests
    print_status "Running frontend tests..."
    cd frontend
    npm test -- --watchAll=false
    cd ..
    
    print_success "All tests completed"
}

# Run linting
run_lint() {
    print_status "Running linting..."
    
    # Backend linting
    cd backend
    npm run lint
    cd ..
    
    # Frontend linting
    cd frontend
    npm run lint
    cd ..
    
    print_success "Linting completed"
}

# Format code
format_code() {
    print_status "Formatting code..."
    
    # Backend formatting
    cd backend
    npm run lint:fix
    cd ..
    
    # Frontend formatting
    cd frontend
    npm run lint:fix
    cd ..
    
    print_success "Code formatting completed"
}

# Show service status
show_status() {
    print_status "Service status:"
    docker-compose -f docker-compose.dev.yml ps
}

# Open shell in service
open_shell() {
    if [ -z "$1" ]; then
        print_error "Please specify a service name"
        echo "Available services: backend, frontend, postgres, redis, minio"
        exit 1
    fi
    
    print_status "Opening shell in $1 container..."
    docker-compose -f docker-compose.dev.yml exec "$1" /bin/bash
}

# Main function
main() {
    case "$1" in
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs "$2"
            ;;
        "build")
            build_images
            ;;
        "clean")
            clean_up
            ;;
        "db:reset")
            reset_database
            ;;
        "db:seed")
            seed_database
            ;;
        "db:migrate")
            run_migrations
            ;;
        "test")
            run_tests
            ;;
        "lint")
            run_lint
            ;;
        "format")
            format_code
            ;;
        "status")
            show_status
            ;;
        "shell")
            open_shell "$2"
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
