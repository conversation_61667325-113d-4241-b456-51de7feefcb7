# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/ngs_lms?schema=public"
REDIS_URL="redis://localhost:6379"

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_REFRESH_EXPIRES_IN=30d

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.txt,.zip,.tar.gz,.fasta,.fastq,.fa,.fq

# MinIO Configuration (Object Storage)
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=ngs-lms-files
MINIO_USE_SSL=false

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# Docker Configuration
DOCKER_HOST=unix:///var/run/docker.sock
PRACTICE_CONTAINER_IMAGE=ngs-lms/practice-env:latest
CONTAINER_MEMORY_LIMIT=2g
CONTAINER_CPU_LIMIT=1

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3001
CORS_CREDENTIALS=true

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Practice Environment
HPC_SIMULATION_ENABLED=true
MAX_CONCURRENT_CONTAINERS=50
CONTAINER_TIMEOUT_MINUTES=120

# Assessment Configuration
AUTO_GRADING_ENABLED=true
PLAGIARISM_CHECK_ENABLED=true
SIMILARITY_THRESHOLD=0.8
