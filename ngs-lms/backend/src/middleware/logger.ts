import { Request, Response, NextFunction } from 'express';
import fs from 'fs';
import path from 'path';
import { config } from '../config/app';

// Ensure logs directory exists
const logsDir = path.dirname(config.logging.file);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

/**
 * Log levels
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

/**
 * Logger interface
 */
interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  meta?: any;
  requestId?: string;
  userId?: string;
  ip?: string;
  userAgent?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
}

/**
 * Simple logger class
 */
class Logger {
  private logLevel: LogLevel;
  private logFile: string;

  constructor() {
    this.logLevel = config.logging.level as LogLevel || LogLevel.INFO;
    this.logFile = config.logging.file;
  }

  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.ERROR, LogLevel.WARN, LogLevel.INFO, LogLevel.DEBUG];
    const currentLevelIndex = levels.indexOf(this.logLevel);
    const messageLevelIndex = levels.indexOf(level);
    return messageLevelIndex <= currentLevelIndex;
  }

  private formatLogEntry(entry: LogEntry): string {
    return JSON.stringify(entry) + '\n';
  }

  private writeToFile(entry: LogEntry): void {
    if (this.logFile) {
      const logString = this.formatLogEntry(entry);
      fs.appendFileSync(this.logFile, logString);
    }
  }

  private log(level: LogLevel, message: string, meta?: any): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      meta,
    };

    // Console output with colors
    const colors = {
      [LogLevel.ERROR]: '\x1b[31m', // Red
      [LogLevel.WARN]: '\x1b[33m',  // Yellow
      [LogLevel.INFO]: '\x1b[36m',  // Cyan
      [LogLevel.DEBUG]: '\x1b[37m', // White
    };

    const resetColor = '\x1b[0m';
    const color = colors[level] || resetColor;

    console.log(
      `${color}[${entry.timestamp}] ${level.toUpperCase()}: ${message}${resetColor}`,
      meta ? meta : ''
    );

    // Write to file
    this.writeToFile(entry);
  }

  error(message: string, meta?: any): void {
    this.log(LogLevel.ERROR, message, meta);
  }

  warn(message: string, meta?: any): void {
    this.log(LogLevel.WARN, message, meta);
  }

  info(message: string, meta?: any): void {
    this.log(LogLevel.INFO, message, meta);
  }

  debug(message: string, meta?: any): void {
    this.log(LogLevel.DEBUG, message, meta);
  }
}

// Create logger instance
export const logger = new Logger();

/**
 * Request logging middleware
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(7);

  // Add request ID to request object
  (req as any).requestId = requestId;

  // Log request start
  logger.info('Request started', {
    requestId,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const responseTime = Date.now() - startTime;

    logger.info('Request completed', {
      requestId,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime,
      ip: req.ip,
      userId: (req as any).user?.id,
    });

    return originalEnd.call(this, chunk, encoding);
  };

  next();
};

/**
 * Error logging middleware
 */
export const errorLogger = (error: Error, req: Request, res: Response, next: NextFunction): void => {
  const requestId = (req as any).requestId;

  logger.error('Request error', {
    requestId,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
    },
    user: (req as any).user?.id,
    ip: req.ip,
  });

  next(error);
};

/**
 * Security event logger
 */
export const logSecurityEvent = (
  event: string,
  req: Request,
  details?: any
): void => {
  logger.warn(`Security event: ${event}`, {
    requestId: (req as any).requestId,
    event,
    details,
    request: {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    },
    user: (req as any).user?.id,
  });
};

/**
 * Database operation logger
 */
export const logDatabaseOperation = (
  operation: string,
  table: string,
  details?: any
): void => {
  logger.debug(`Database operation: ${operation}`, {
    operation,
    table,
    details,
  });
};

/**
 * Practice environment logger
 */
export const logPracticeActivity = (
  userId: string,
  action: string,
  details?: any
): void => {
  logger.info(`Practice activity: ${action}`, {
    userId,
    action,
    details,
    timestamp: new Date().toISOString(),
  });
};

/**
 * File operation logger
 */
export const logFileOperation = (
  operation: string,
  filename: string,
  userId?: string,
  details?: any
): void => {
  logger.info(`File operation: ${operation}`, {
    operation,
    filename,
    userId,
    details,
  });
};

/**
 * Authentication logger
 */
export const logAuthEvent = (
  event: string,
  userId?: string,
  email?: string,
  ip?: string,
  details?: any
): void => {
  logger.info(`Auth event: ${event}`, {
    event,
    userId,
    email,
    ip,
    details,
  });
};
