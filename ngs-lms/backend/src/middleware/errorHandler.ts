import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { ValidationError } from 'joi';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

/**
 * Custom error class for application errors
 */
export class CustomError extends Error implements AppError {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Handle Prisma database errors
 */
const handlePrismaError = (error: Prisma.PrismaClientKnownRequestError): CustomError => {
  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const field = error.meta?.target as string[] | undefined;
      const fieldName = field ? field[0] : 'field';
      return new CustomError(`${fieldName} already exists`, 409);
    
    case 'P2025':
      // Record not found
      return new CustomError('Record not found', 404);
    
    case 'P2003':
      // Foreign key constraint violation
      return new CustomError('Related record not found', 400);
    
    case 'P2014':
      // Invalid ID
      return new CustomError('Invalid ID provided', 400);
    
    case 'P2021':
      // Table does not exist
      return new CustomError('Database table not found', 500);
    
    case 'P2022':
      // Column does not exist
      return new CustomError('Database column not found', 500);
    
    default:
      return new CustomError('Database operation failed', 500);
  }
};

/**
 * Handle Joi validation errors
 */
const handleValidationError = (error: ValidationError): CustomError => {
  const message = error.details.map(detail => detail.message).join(', ');
  return new CustomError(`Validation error: ${message}`, 400);
};

/**
 * Handle JWT errors
 */
const handleJWTError = (error: Error): CustomError => {
  if (error.name === 'JsonWebTokenError') {
    return new CustomError('Invalid token', 401);
  }
  if (error.name === 'TokenExpiredError') {
    return new CustomError('Token expired', 401);
  }
  return new CustomError('Authentication failed', 401);
};

/**
 * Handle multer file upload errors
 */
const handleMulterError = (error: any): CustomError => {
  if (error.code === 'LIMIT_FILE_SIZE') {
    return new CustomError('File too large', 413);
  }
  if (error.code === 'LIMIT_FILE_COUNT') {
    return new CustomError('Too many files', 413);
  }
  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return new CustomError('Unexpected file field', 400);
  }
  return new CustomError('File upload failed', 400);
};

/**
 * Send error response in development
 */
const sendErrorDev = (err: AppError, res: Response): void => {
  res.status(err.statusCode || 500).json({
    success: false,
    error: {
      message: err.message,
      stack: err.stack,
      statusCode: err.statusCode,
      isOperational: err.isOperational,
    },
  });
};

/**
 * Send error response in production
 */
const sendErrorProd = (err: AppError, res: Response): void => {
  // Only send operational errors to client in production
  if (err.isOperational) {
    res.status(err.statusCode || 500).json({
      success: false,
      message: err.message,
    });
  } else {
    // Log error for debugging
    console.error('ERROR 💥', err);
    
    // Send generic message
    res.status(500).json({
      success: false,
      message: 'Something went wrong!',
    });
  }
};

/**
 * Global error handling middleware
 */
export const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  console.error('Error:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // Handle specific error types
  if (err instanceof Prisma.PrismaClientKnownRequestError) {
    error = handlePrismaError(err);
  } else if (err.name === 'ValidationError') {
    error = handleValidationError(err);
  } else if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    error = handleJWTError(err);
  } else if (err.name === 'MulterError') {
    error = handleMulterError(err);
  } else if (err.name === 'CastError') {
    error = new CustomError('Invalid ID format', 400);
  } else if (err.code === 'ENOENT') {
    error = new CustomError('File not found', 404);
  } else if (err.code === 'EACCES') {
    error = new CustomError('Permission denied', 403);
  } else if (!err.statusCode) {
    // Unknown error
    error = new CustomError('Internal server error', 500, false);
  }

  // Send error response
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(error, res);
  } else {
    sendErrorProd(error, res);
  }
};

/**
 * Async error wrapper to catch async errors
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Handle 404 errors
 */
export const notFound = (req: Request, res: Response, next: NextFunction): void => {
  const error = new CustomError(`Route ${req.originalUrl} not found`, 404);
  next(error);
};

/**
 * Create custom error
 */
export const createError = (message: string, statusCode: number = 500): CustomError => {
  return new CustomError(message, statusCode);
};
