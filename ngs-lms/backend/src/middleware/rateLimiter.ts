import { Request, Response, NextFunction } from 'express';
import { RateLimiterRedis } from 'rate-limiter-flexible';
import { redisClient } from '../config/database';
import { config } from '../config/app';

// Create rate limiter instances
const rateLimiterRedis = new RateLimiterRedis({
  storeClient: redisClient,
  keyPrefix: 'rl_global',
  points: config.rateLimit.maxRequests, // Number of requests
  duration: Math.floor(config.rateLimit.windowMs / 1000), // Per duration in seconds
  blockDuration: 60, // Block for 60 seconds if limit exceeded
});

// Strict rate limiter for auth endpoints
const authRateLimiter = new RateLimiterRedis({
  storeClient: redisClient,
  keyPrefix: 'rl_auth',
  points: 5, // 5 attempts
  duration: 900, // Per 15 minutes
  blockDuration: 900, // Block for 15 minutes
});

// Rate limiter for file uploads
const uploadRateLimiter = new RateLimiterRedis({
  storeClient: redisClient,
  keyPrefix: 'rl_upload',
  points: 10, // 10 uploads
  duration: 3600, // Per hour
  blockDuration: 3600, // Block for 1 hour
});

// Rate limiter for practice environment
const practiceRateLimiter = new RateLimiterRedis({
  storeClient: redisClient,
  keyPrefix: 'rl_practice',
  points: 100, // 100 commands
  duration: 300, // Per 5 minutes
  blockDuration: 300, // Block for 5 minutes
});

/**
 * Get client identifier for rate limiting
 */
const getClientId = (req: Request): string => {
  // Use user ID if authenticated, otherwise use IP
  const userId = (req as any).user?.id;
  return userId || req.ip || req.connection.remoteAddress || 'unknown';
};

/**
 * Create rate limiter middleware
 */
const createRateLimiterMiddleware = (limiter: RateLimiterRedis, message?: string) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const clientId = getClientId(req);
      await limiter.consume(clientId);
      next();
    } catch (rateLimiterRes: any) {
      const remainingPoints = rateLimiterRes?.remainingPoints || 0;
      const msBeforeNext = rateLimiterRes?.msBeforeNext || 0;
      const totalHits = rateLimiterRes?.totalHits || 0;

      // Set rate limit headers
      res.set({
        'Retry-After': Math.round(msBeforeNext / 1000) || 1,
        'X-RateLimit-Limit': limiter.points,
        'X-RateLimit-Remaining': remainingPoints,
        'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
      });

      res.status(429).json({
        success: false,
        message: message || 'Too many requests, please try again later',
        retryAfter: Math.round(msBeforeNext / 1000),
        limit: limiter.points,
        remaining: remainingPoints,
        reset: new Date(Date.now() + msBeforeNext).toISOString(),
      });
    }
  };
};

/**
 * Global rate limiter middleware
 */
export const rateLimiter = createRateLimiterMiddleware(
  rateLimiterRedis,
  'Too many requests from this IP, please try again later'
);

/**
 * Authentication rate limiter middleware
 */
export const authRateLimit = createRateLimiterMiddleware(
  authRateLimiter,
  'Too many authentication attempts, please try again later'
);

/**
 * Upload rate limiter middleware
 */
export const uploadRateLimit = createRateLimiterMiddleware(
  uploadRateLimiter,
  'Too many file uploads, please try again later'
);

/**
 * Practice environment rate limiter middleware
 */
export const practiceRateLimit = createRateLimiterMiddleware(
  practiceRateLimiter,
  'Too many practice commands, please slow down'
);

/**
 * Custom rate limiter for specific endpoints
 */
export const customRateLimit = (
  points: number,
  duration: number,
  blockDuration?: number,
  message?: string
) => {
  const limiter = new RateLimiterRedis({
    storeClient: redisClient,
    keyPrefix: 'rl_custom',
    points,
    duration,
    blockDuration: blockDuration || duration,
  });

  return createRateLimiterMiddleware(limiter, message);
};

/**
 * Rate limiter for API endpoints based on user role
 */
export const roleBasedRateLimit = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const user = (req as any).user;

  if (!user) {
    // Apply strict limits for unauthenticated users
    return rateLimiter(req, res, next);
  }

  // Different limits based on user role
  let limiter: RateLimiterRedis;

  switch (user.role) {
    case 'admin':
      // Admins get higher limits
      limiter = new RateLimiterRedis({
        storeClient: redisClient,
        keyPrefix: 'rl_admin',
        points: 1000,
        duration: 3600,
        blockDuration: 60,
      });
      break;

    case 'instructor':
      // Instructors get moderate limits
      limiter = new RateLimiterRedis({
        storeClient: redisClient,
        keyPrefix: 'rl_instructor',
        points: 500,
        duration: 3600,
        blockDuration: 300,
      });
      break;

    case 'student':
    default:
      // Students get standard limits
      limiter = new RateLimiterRedis({
        storeClient: redisClient,
        keyPrefix: 'rl_student',
        points: 200,
        duration: 3600,
        blockDuration: 600,
      });
      break;
  }

  const middleware = createRateLimiterMiddleware(limiter);
  return middleware(req, res, next);
};

/**
 * Skip rate limiting for certain conditions
 */
export const skipRateLimit = (req: Request): boolean => {
  // Skip rate limiting for health checks
  if (req.path === '/health') {
    return true;
  }

  // Skip for admin users in development
  if (process.env.NODE_ENV === 'development') {
    const user = (req as any).user;
    if (user && user.role === 'admin') {
      return true;
    }
  }

  return false;
};

/**
 * Conditional rate limiter that can be skipped
 */
export const conditionalRateLimit = (limiter: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (skipRateLimit(req)) {
      return next();
    }
    return limiter(req, res, next);
  };
};
