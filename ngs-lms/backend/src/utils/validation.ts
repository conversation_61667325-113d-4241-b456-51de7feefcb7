import Joi from 'joi';
import { UserRole } from '../types';

// Common validation schemas
const emailSchema = Joi.string().email().required().messages({
  'string.email': 'Please provide a valid email address',
  'any.required': 'Email is required',
});

const passwordSchema = Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]')).required().messages({
  'string.min': 'Password must be at least 8 characters long',
  'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
  'any.required': 'Password is required',
});

const usernameSchema = Joi.string().alphanum().min(3).max(30).required().messages({
  'string.alphanum': 'Username must contain only alphanumeric characters',
  'string.min': 'Username must be at least 3 characters long',
  'string.max': 'Username must not exceed 30 characters',
  'any.required': 'Username is required',
});

// User registration validation
export const validateRegister = (data: any) => {
  const schema = Joi.object({
    username: usernameSchema,
    email: emailSchema,
    password: passwordSchema,
    firstName: Joi.string().min(1).max(50).required().messages({
      'string.min': 'First name is required',
      'string.max': 'First name must not exceed 50 characters',
      'any.required': 'First name is required',
    }),
    lastName: Joi.string().min(1).max(50).required().messages({
      'string.min': 'Last name is required',
      'string.max': 'Last name must not exceed 50 characters',
      'any.required': 'Last name is required',
    }),
    studentId: Joi.string().max(20).optional().allow('').messages({
      'string.max': 'Student ID must not exceed 20 characters',
    }),
    institution: Joi.string().min(1).max(100).required().messages({
      'string.min': 'Institution is required',
      'string.max': 'Institution must not exceed 100 characters',
      'any.required': 'Institution is required',
    }),
    major: Joi.string().min(1).max(100).required().messages({
      'string.min': 'Major is required',
      'string.max': 'Major must not exceed 100 characters',
      'any.required': 'Major is required',
    }),
    role: Joi.string().valid(...Object.values(UserRole)).optional().default(UserRole.STUDENT),
  });

  return schema.validate(data);
};

// User login validation
export const validateLogin = (data: any) => {
  const schema = Joi.object({
    email: emailSchema,
    password: Joi.string().required().messages({
      'any.required': 'Password is required',
    }),
  });

  return schema.validate(data);
};

// User profile update validation
export const validateProfileUpdate = (data: any) => {
  const schema = Joi.object({
    firstName: Joi.string().min(1).max(50).optional(),
    lastName: Joi.string().min(1).max(50).optional(),
    studentId: Joi.string().max(20).optional().allow(''),
    institution: Joi.string().min(1).max(100).optional(),
    major: Joi.string().min(1).max(100).optional(),
    bio: Joi.string().max(500).optional().allow(''),
  });

  return schema.validate(data);
};

// Password change validation
export const validatePasswordChange = (data: any) => {
  const schema = Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': 'Current password is required',
    }),
    newPassword: passwordSchema,
    confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required().messages({
      'any.only': 'Passwords do not match',
      'any.required': 'Password confirmation is required',
    }),
  });

  return schema.validate(data);
};

// Course creation validation
export const validateCourseCreate = (data: any) => {
  const schema = Joi.object({
    title: Joi.string().min(1).max(200).required().messages({
      'string.min': 'Course title is required',
      'string.max': 'Course title must not exceed 200 characters',
      'any.required': 'Course title is required',
    }),
    titleEn: Joi.string().min(1).max(200).optional(),
    description: Joi.string().min(1).max(1000).required().messages({
      'string.min': 'Course description is required',
      'string.max': 'Course description must not exceed 1000 characters',
      'any.required': 'Course description is required',
    }),
    descriptionEn: Joi.string().min(1).max(1000).optional(),
    code: Joi.string().min(1).max(20).required().messages({
      'string.min': 'Course code is required',
      'string.max': 'Course code must not exceed 20 characters',
      'any.required': 'Course code is required',
    }),
    credits: Joi.number().integer().min(1).max(10).required().messages({
      'number.base': 'Credits must be a number',
      'number.integer': 'Credits must be an integer',
      'number.min': 'Credits must be at least 1',
      'number.max': 'Credits must not exceed 10',
      'any.required': 'Credits is required',
    }),
    duration: Joi.number().integer().min(1).max(52).required().messages({
      'number.base': 'Duration must be a number',
      'number.integer': 'Duration must be an integer',
      'number.min': 'Duration must be at least 1 week',
      'number.max': 'Duration must not exceed 52 weeks',
      'any.required': 'Duration is required',
    }),
  });

  return schema.validate(data);
};

// Assignment creation validation
export const validateAssignmentCreate = (data: any) => {
  const schema = Joi.object({
    title: Joi.string().min(1).max(200).required(),
    description: Joi.string().min(1).max(2000).required(),
    type: Joi.string().valid('practice_report', 'quiz', 'project', 'peer_review').required(),
    maxScore: Joi.number().integer().min(1).max(1000).required(),
    dueDate: Joi.date().greater('now').required().messages({
      'date.greater': 'Due date must be in the future',
    }),
    courseId: Joi.string().uuid().required(),
  });

  return schema.validate(data);
};

// Knowledge node validation
export const validateKnowledgeNode = (data: any) => {
  const schema = Joi.object({
    title: Joi.string().min(1).max(200).required(),
    titleEn: Joi.string().min(1).max(200).optional(),
    content: Joi.string().min(1).required(),
    contentEn: Joi.string().min(1).optional(),
    mediaType: Joi.string().valid('text', 'image', 'video', 'animation', 'interactive').required(),
    prerequisites: Joi.array().items(Joi.string().uuid()).optional().default([]),
    relatedConcepts: Joi.array().items(Joi.string().uuid()).optional().default([]),
    difficulty: Joi.string().valid('basic', 'intermediate', 'advanced').required(),
    tags: Joi.array().items(Joi.string().max(50)).optional().default([]),
  });

  return schema.validate(data);
};

// Practice command validation
export const validatePracticeCommand = (data: any) => {
  const schema = Joi.object({
    command: Joi.string().min(1).max(1000).required().messages({
      'string.min': 'Command is required',
      'string.max': 'Command is too long',
      'any.required': 'Command is required',
    }),
    environmentId: Joi.string().uuid().required(),
    lessonId: Joi.string().uuid().optional(),
  });

  return schema.validate(data);
};

// File upload validation
export const validateFileUpload = (data: any) => {
  const schema = Joi.object({
    filename: Joi.string().min(1).max(255).required(),
    mimeType: Joi.string().required(),
    size: Joi.number().integer().min(1).max(100 * 1024 * 1024).required(), // Max 100MB
    assignmentId: Joi.string().uuid().optional(),
    lessonId: Joi.string().uuid().optional(),
  });

  return schema.validate(data);
};

// Pagination validation
export const validatePagination = (data: any) => {
  const schema = Joi.object({
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').optional().default('desc'),
  });

  return schema.validate(data);
};

// Search validation
export const validateSearch = (data: any) => {
  const schema = Joi.object({
    q: Joi.string().min(1).max(100).optional(),
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').optional().default('desc'),
    filters: Joi.object().optional(),
  });

  return schema.validate(data);
};

// Message validation
export const validateMessage = (data: any) => {
  const schema = Joi.object({
    content: Joi.string().min(1).max(2000).required().messages({
      'string.min': 'Message content is required',
      'string.max': 'Message is too long (max 2000 characters)',
      'any.required': 'Message content is required',
    }),
    receiverId: Joi.string().uuid().optional(),
    courseId: Joi.string().uuid().optional(),
    type: Joi.string().valid('direct', 'forum', 'announcement', 'system').required(),
  });

  return schema.validate(data);
};

// Environment configuration validation
export const validateEnvironmentConfig = (data: any) => {
  const schema = Joi.object({
    softwareStack: Joi.object({
      conda: Joi.string().optional(),
      fastqc: Joi.string().optional(),
      jellyfish: Joi.string().optional(),
      flye: Joi.string().optional(),
      prokka: Joi.string().optional(),
      quast: Joi.string().optional(),
      checkm: Joi.string().optional(),
      fastani: Joi.string().optional(),
      gtotree: Joi.string().optional(),
      mashtree: Joi.string().optional(),
    }).optional(),
    datasetPath: Joi.string().optional(),
    memoryLimit: Joi.string().pattern(/^\d+[gmk]$/i).optional(),
    cpuLimit: Joi.string().optional(),
    timeoutMinutes: Joi.number().integer().min(1).max(480).optional(),
  });

  return schema.validate(data);
};
