// User Types
export enum UserRole {
  STUDENT = 'student',
  INSTRUCTOR = 'instructor',
  ADMIN = 'admin',
  GUEST = 'guest'
}

export interface User {
  id: string;
  username: string;
  email: string;
  password?: string;
  role: UserRole;
  profile: UserProfile;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  studentId?: string;
  institution: string;
  major: string;
  avatar?: string;
  bio?: string;
}

// Course Types
export interface Course {
  id: string;
  title: string;
  titleEn: string;
  description: string;
  descriptionEn: string;
  code: string;
  credits: number;
  duration: number; // weeks
  isActive: boolean;
  instructorId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CourseModule {
  id: string;
  courseId: string;
  title: string;
  titleEn: string;
  description: string;
  descriptionEn: string;
  order: number;
  isActive: boolean;
  lessons: Lesson[];
}

export interface Lesson {
  id: string;
  moduleId: string;
  title: string;
  titleEn: string;
  content: string;
  contentEn: string;
  type: LessonType;
  order: number;
  duration: number; // minutes
  isActive: boolean;
  resources: LessonResource[];
}

export enum LessonType {
  THEORY = 'theory',
  PRACTICE = 'practice',
  QUIZ = 'quiz',
  ASSIGNMENT = 'assignment'
}

export interface LessonResource {
  id: string;
  lessonId: string;
  title: string;
  type: ResourceType;
  url: string;
  size?: number;
  mimeType?: string;
}

export enum ResourceType {
  VIDEO = 'video',
  DOCUMENT = 'document',
  IMAGE = 'image',
  LINK = 'link',
  DATASET = 'dataset'
}

// Knowledge Base Types
export interface KnowledgeNode {
  id: string;
  title: string;
  titleEn: string;
  content: string;
  contentEn: string;
  mediaType: MediaType;
  prerequisites: string[];
  relatedConcepts: string[];
  difficulty: DifficultyLevel;
  tags: string[];
  isActive: boolean;
}

export enum MediaType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  ANIMATION = 'animation',
  INTERACTIVE = 'interactive'
}

export enum DifficultyLevel {
  BASIC = 'basic',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
}

// Practice Environment Types
export interface PracticeEnvironment {
  id: string;
  userId: string;
  containerId: string;
  sessionId: string;
  status: ContainerStatus;
  softwareStack: SoftwareStack;
  datasetPath: string;
  workingDirectory: string;
  createdAt: Date;
  expiresAt: Date;
}

export enum ContainerStatus {
  CREATING = 'creating',
  RUNNING = 'running',
  STOPPED = 'stopped',
  ERROR = 'error',
  EXPIRED = 'expired'
}

export interface SoftwareStack {
  conda: string;
  fastqc: string;
  jellyfish: string;
  flye: string;
  prokka: string;
  quast: string;
  checkm: string;
  fastani: string;
  gtotree: string;
  mashtree: string;
}

// Command Validation Types
export interface CommandValidation {
  command: string;
  isValid: boolean;
  syntax: SyntaxCheck;
  parameters: ParameterCheck;
  expectedOutput?: ExpectedOutput;
  suggestions?: string[];
}

export interface SyntaxCheck {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ParameterCheck {
  isValid: boolean;
  missingRequired: string[];
  invalidValues: string[];
  suggestions: ParameterSuggestion[];
}

export interface ParameterSuggestion {
  parameter: string;
  suggestedValue: string;
  reason: string;
}

export interface ExpectedOutput {
  type: OutputType;
  pattern?: string;
  files?: string[];
  exitCode?: number;
}

export enum OutputType {
  FILE = 'file',
  STDOUT = 'stdout',
  STDERR = 'stderr',
  EXIT_CODE = 'exit_code'
}

// Assignment Types
export interface Assignment {
  id: string;
  courseId: string;
  title: string;
  description: string;
  type: AssignmentType;
  maxScore: number;
  dueDate: Date;
  isActive: boolean;
  template: AssignmentTemplate;
  rubric: GradingRubric;
}

export enum AssignmentType {
  PRACTICE_REPORT = 'practice_report',
  QUIZ = 'quiz',
  PROJECT = 'project',
  PEER_REVIEW = 'peer_review'
}

export interface AssignmentTemplate {
  sections: TemplateSection[];
  requiredFiles: string[];
  maxFileSize: number;
}

export interface TemplateSection {
  id: string;
  title: string;
  description: string;
  isRequired: boolean;
  type: SectionType;
  placeholder?: string;
}

export enum SectionType {
  TEXT = 'text',
  CODE = 'code',
  FILE_UPLOAD = 'file_upload',
  MULTIPLE_CHOICE = 'multiple_choice',
  CHECKBOX = 'checkbox'
}

export interface GradingRubric {
  criteria: RubricCriterion[];
  totalPoints: number;
}

export interface RubricCriterion {
  id: string;
  name: string;
  description: string;
  maxPoints: number;
  levels: RubricLevel[];
}

export interface RubricLevel {
  score: number;
  description: string;
}

// Submission Types
export interface Submission {
  id: string;
  assignmentId: string;
  userId: string;
  content: SubmissionContent;
  files: SubmissionFile[];
  status: SubmissionStatus;
  score?: number;
  feedback?: string;
  submittedAt: Date;
  gradedAt?: Date;
  gradedBy?: string;
}

export interface SubmissionContent {
  sections: { [sectionId: string]: any };
  metadata: SubmissionMetadata;
}

export interface SubmissionMetadata {
  commandHistory: CommandRecord[];
  timeSpent: number; // minutes
  environment: string;
  softwareVersions: { [tool: string]: string };
}

export interface CommandRecord {
  command: string;
  timestamp: Date;
  exitCode: number;
  output?: string;
  error?: string;
}

export interface SubmissionFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  path: string;
  uploadedAt: Date;
}

export enum SubmissionStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  GRADING = 'grading',
  GRADED = 'graded',
  RETURNED = 'returned'
}

// Progress Tracking Types
export interface UserProgress {
  userId: string;
  courseId: string;
  completedLessons: string[];
  completedAssignments: string[];
  totalTimeSpent: number; // minutes
  lastActivity: Date;
  overallProgress: number; // percentage
  moduleProgress: { [moduleId: string]: number };
}

// Communication Types
export interface Message {
  id: string;
  senderId: string;
  receiverId?: string;
  courseId?: string;
  content: string;
  type: MessageType;
  isRead: boolean;
  createdAt: Date;
}

export enum MessageType {
  DIRECT = 'direct',
  FORUM = 'forum',
  ANNOUNCEMENT = 'announcement',
  SYSTEM = 'system'
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Request Types
export interface AuthRequest extends Request {
  user?: User;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SearchQuery extends PaginationQuery {
  q?: string;
  filters?: { [key: string]: any };
}
