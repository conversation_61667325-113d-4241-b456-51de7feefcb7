import dotenv from 'dotenv';

dotenv.config();

export const config = {
  // Server Configuration
  server: {
    port: parseInt(process.env.PORT || '3000', 10),
    host: process.env.HOST || 'localhost',
    nodeEnv: process.env.NODE_ENV || 'development',
  },

  // Database Configuration
  database: {
    url: process.env.DATABASE_URL || 'postgresql://username:password@localhost:5432/ngs_lms',
    redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  },

  // File Upload Configuration
  upload: {
    dir: process.env.UPLOAD_DIR || './uploads',
    maxFileSize: process.env.MAX_FILE_SIZE || '100MB',
    allowedTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || [
      '.pdf', '.doc', '.docx', '.txt', '.zip', '.tar.gz',
      '.fasta', '.fastq', '.fa', '.fq', '.gz'
    ],
  },

  // MinIO Configuration
  minio: {
    endpoint: process.env.MINIO_ENDPOINT || 'localhost',
    port: parseInt(process.env.MINIO_PORT || '9000', 10),
    accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
    secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin',
    bucketName: process.env.MINIO_BUCKET_NAME || 'ngs-lms-files',
    useSSL: process.env.MINIO_USE_SSL === 'true',
  },

  // Email Configuration
  email: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587', 10),
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || '',
    from: process.env.FROM_EMAIL || '<EMAIL>',
  },

  // Docker Configuration
  docker: {
    host: process.env.DOCKER_HOST || 'unix:///var/run/docker.sock',
    practiceImage: process.env.PRACTICE_CONTAINER_IMAGE || 'ngs-lms/practice-env:latest',
    memoryLimit: process.env.CONTAINER_MEMORY_LIMIT || '2g',
    cpuLimit: process.env.CONTAINER_CPU_LIMIT || '1',
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },

  // CORS Configuration
  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3001',
    credentials: process.env.CORS_CREDENTIALS === 'true',
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log',
  },

  // Practice Environment Configuration
  practice: {
    hpcSimulationEnabled: process.env.HPC_SIMULATION_ENABLED === 'true',
    maxConcurrentContainers: parseInt(process.env.MAX_CONCURRENT_CONTAINERS || '50', 10),
    containerTimeoutMinutes: parseInt(process.env.CONTAINER_TIMEOUT_MINUTES || '120', 10),
  },

  // Assessment Configuration
  assessment: {
    autoGradingEnabled: process.env.AUTO_GRADING_ENABLED === 'true',
    plagiarismCheckEnabled: process.env.PLAGIARISM_CHECK_ENABLED === 'true',
    similarityThreshold: parseFloat(process.env.SIMILARITY_THRESHOLD || '0.8'),
  },

  // Security Configuration
  security: {
    bcryptRounds: 12,
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
  },

  // Pagination Defaults
  pagination: {
    defaultLimit: 20,
    maxLimit: 100,
  },

  // Cache Configuration
  cache: {
    defaultTTL: 3600, // 1 hour
    userSessionTTL: 1800, // 30 minutes
    courseTTL: 7200, // 2 hours
  },
};

// Validation function to ensure required environment variables are set
export const validateConfig = (): void => {
  const requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars);
    process.exit(1);
  }

  // Validate JWT secrets are not default values in production
  if (config.server.nodeEnv === 'production') {
    if (config.jwt.secret === 'your-super-secret-jwt-key' ||
        config.jwt.refreshSecret === 'your-refresh-secret-key') {
      console.error('❌ Default JWT secrets detected in production environment');
      process.exit(1);
    }
  }

  console.log('✅ Configuration validated successfully');
};

export default config;
