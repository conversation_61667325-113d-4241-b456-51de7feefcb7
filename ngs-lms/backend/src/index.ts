import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

import { config, validateConfig } from './config/app';
import { connectDatabase, connectRedis, checkDatabaseHealth } from './config/database';
import { errorHandler } from './middleware/errorHandler';
import { rateLimiter } from './middleware/rateLimiter';
import { requestLogger } from './middleware/logger';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import courseRoutes from './routes/courses';
import practiceRoutes from './routes/practice';
import assignmentRoutes from './routes/assignments';
import knowledgeRoutes from './routes/knowledge';

class App {
  public app: express.Application;
  public server: any;
  public io: SocketIOServer;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: config.cors.origin,
        credentials: config.cors.credentials,
      },
    });

    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeSwagger();
    this.initializeErrorHandling();
    this.initializeSocketIO();
  }

  private initializeMiddlewares(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: config.cors.credentials,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Compression middleware
    this.app.use(compression());

    // Request logging
    this.app.use(morgan('combined'));
    this.app.use(requestLogger);

    // Rate limiting
    this.app.use(rateLimiter);

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Static files
    this.app.use('/uploads', express.static(config.upload.dir));
  }

  private initializeRoutes(): void {
    // Health check endpoint
    this.app.get('/health', async (req, res) => {
      const health = await checkDatabaseHealth();
      const status = health.postgres && health.redis ? 200 : 503;
      
      res.status(status).json({
        status: status === 200 ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        services: {
          postgres: health.postgres ? 'up' : 'down',
          redis: health.redis ? 'up' : 'down',
        },
        version: process.env.npm_package_version || '1.0.0',
      });
    });

    // API routes
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/users', userRoutes);
    this.app.use('/api/courses', courseRoutes);
    this.app.use('/api/practice', practiceRoutes);
    this.app.use('/api/assignments', assignmentRoutes);
    this.app.use('/api/knowledge', knowledgeRoutes);

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        message: 'Route not found',
        path: req.originalUrl,
      });
    });
  }

  private initializeSwagger(): void {
    const options = {
      definition: {
        openapi: '3.0.0',
        info: {
          title: 'NGS LMS API',
          version: '1.0.0',
          description: 'API documentation for NGS Learning Management System',
          contact: {
            name: 'Wang Yunsheng',
            email: '<EMAIL>',
          },
        },
        servers: [
          {
            url: `http://${config.server.host}:${config.server.port}`,
            description: 'Development server',
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
        security: [
          {
            bearerAuth: [],
          },
        ],
      },
      apis: ['./src/routes/*.ts', './src/controllers/*.ts'],
    };

    const specs = swaggerJsdoc(options);
    this.app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: 'NGS LMS API Documentation',
    }));
  }

  private initializeErrorHandling(): void {
    this.app.use(errorHandler);
  }

  private initializeSocketIO(): void {
    this.io.on('connection', (socket) => {
      console.log(`User connected: ${socket.id}`);

      // Join user to their personal room
      socket.on('join-user-room', (userId: string) => {
        socket.join(`user-${userId}`);
        console.log(`User ${userId} joined personal room`);
      });

      // Join course room
      socket.on('join-course-room', (courseId: string) => {
        socket.join(`course-${courseId}`);
        console.log(`User joined course room: ${courseId}`);
      });

      // Handle practice environment events
      socket.on('practice-command', (data) => {
        // Handle command execution in practice environment
        console.log('Practice command received:', data);
        // Emit response back to user
        socket.emit('practice-response', {
          success: true,
          output: 'Command executed successfully',
        });
      });

      // Handle chat messages
      socket.on('send-message', (data) => {
        // Broadcast message to course room
        socket.to(`course-${data.courseId}`).emit('new-message', data);
      });

      socket.on('disconnect', () => {
        console.log(`User disconnected: ${socket.id}`);
      });
    });
  }

  public async start(): Promise<void> {
    try {
      // Validate configuration
      validateConfig();

      // Connect to databases
      await connectDatabase();
      await connectRedis();

      // Start server
      this.server.listen(config.server.port, config.server.host, () => {
        console.log(`
🚀 NGS LMS Server is running!
📍 Server: http://${config.server.host}:${config.server.port}
📚 API Docs: http://${config.server.host}:${config.server.port}/api-docs
🔧 Environment: ${config.server.nodeEnv}
        `);
      });

    } catch (error) {
      console.error('❌ Failed to start server:', error);
      process.exit(1);
    }
  }
}

// Create and start the application
const app = new App();
app.start();

export default app;
