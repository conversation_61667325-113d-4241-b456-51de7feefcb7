import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { prisma, cacheSet, cacheDel } from '../config/database';
import { config } from '../config/app';
import { 
  generateToken, 
  generateRefreshToken, 
  verifyRefreshToken 
} from '../middleware/auth';
import { UserRole, AuthRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { logAuthEvent } from '../middleware/logger';
import { validateRegister, validateLogin } from '../utils/validation';

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *               - institution
 *               - major
 *             properties:
 *               username:
 *                 type: string
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               studentId:
 *                 type: string
 *               institution:
 *                 type: string
 *               major:
 *                 type: string
 *               role:
 *                 type: string
 *                 enum: [student, instructor]
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Validation error
 *       409:
 *         description: User already exists
 */
export const register = asyncHandler(async (req: Request, res: Response) => {
  // Validate input
  const { error, value } = validateRegister(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: error.details.map(detail => detail.message),
    });
  }

  const {
    username,
    email,
    password,
    firstName,
    lastName,
    studentId,
    institution,
    major,
    role = UserRole.STUDENT,
  } = value;

  // Check if user already exists
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [
        { email },
        { username },
      ],
    },
  });

  if (existingUser) {
    logAuthEvent('registration_failed', undefined, email, req.ip, { reason: 'user_exists' });
    return res.status(409).json({
      success: false,
      message: 'User with this email or username already exists',
    });
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(password, config.security.bcryptRounds);

  // Create user with profile
  const user = await prisma.user.create({
    data: {
      username,
      email,
      password: hashedPassword,
      role,
      profile: {
        create: {
          firstName,
          lastName,
          studentId,
          institution,
          major,
        },
      },
    },
    include: {
      profile: true,
    },
  });

  // Generate tokens
  const tokenPayload = {
    userId: user.id,
    email: user.email,
    role: user.role as UserRole,
  };

  const accessToken = generateToken(tokenPayload);
  const refreshToken = generateRefreshToken(tokenPayload);

  // Store refresh token in cache
  await cacheSet(`refresh_token:${user.id}`, refreshToken, 30 * 24 * 60 * 60); // 30 days

  logAuthEvent('registration_success', user.id, email, req.ip);

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        profile: user.profile,
        isActive: user.isActive,
        createdAt: user.createdAt,
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: config.jwt.expiresIn,
      },
    },
  });
});

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       400:
 *         description: Validation error
 *       401:
 *         description: Invalid credentials
 */
export const login = asyncHandler(async (req: Request, res: Response) => {
  // Validate input
  const { error, value } = validateLogin(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: error.details.map(detail => detail.message),
    });
  }

  const { email, password } = value;

  // Find user
  const user = await prisma.user.findUnique({
    where: { email },
    include: {
      profile: true,
    },
  });

  if (!user || !user.isActive) {
    logAuthEvent('login_failed', undefined, email, req.ip, { reason: 'user_not_found' });
    return res.status(401).json({
      success: false,
      message: 'Invalid email or password',
    });
  }

  // Check password
  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    logAuthEvent('login_failed', user.id, email, req.ip, { reason: 'invalid_password' });
    return res.status(401).json({
      success: false,
      message: 'Invalid email or password',
    });
  }

  // Update last login
  await prisma.user.update({
    where: { id: user.id },
    data: { lastLogin: new Date() },
  });

  // Generate tokens
  const tokenPayload = {
    userId: user.id,
    email: user.email,
    role: user.role as UserRole,
  };

  const accessToken = generateToken(tokenPayload);
  const refreshToken = generateRefreshToken(tokenPayload);

  // Store refresh token in cache
  await cacheSet(`refresh_token:${user.id}`, refreshToken, 30 * 24 * 60 * 60); // 30 days

  logAuthEvent('login_success', user.id, email, req.ip);

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        profile: user.profile,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: config.jwt.expiresIn,
      },
    },
  });
});

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
export const refreshToken = asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return res.status(401).json({
      success: false,
      message: 'Refresh token is required',
    });
  }

  try {
    // Verify refresh token
    const decoded = verifyRefreshToken(refreshToken);

    // Check if refresh token exists in cache
    const cachedToken = await cacheGet(`refresh_token:${decoded.userId}`);
    if (!cachedToken || cachedToken !== refreshToken) {
      return res.status(401).json({
        success: false,
        message: 'Invalid refresh token',
      });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: { profile: true },
    });

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'User not found or inactive',
      });
    }

    // Generate new tokens
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role as UserRole,
    };

    const newAccessToken = generateToken(tokenPayload);
    const newRefreshToken = generateRefreshToken(tokenPayload);

    // Update refresh token in cache
    await cacheSet(`refresh_token:${user.id}`, newRefreshToken, 30 * 24 * 60 * 60);

    logAuthEvent('token_refresh', user.id, user.email, req.ip);

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        tokens: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          expiresIn: config.jwt.expiresIn,
        },
      },
    });
  } catch (error) {
    logAuthEvent('token_refresh_failed', undefined, undefined, req.ip, { error: error.message });
    return res.status(401).json({
      success: false,
      message: 'Invalid refresh token',
    });
  }
});

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 *       401:
 *         description: Authentication required
 */
export const logout = asyncHandler(async (req: AuthRequest, res: Response) => {
  const userId = req.user?.id;

  if (userId) {
    // Remove refresh token from cache
    await cacheDel(`refresh_token:${userId}`);
    logAuthEvent('logout', userId, req.user?.email, req.ip);
  }

  res.json({
    success: true,
    message: 'Logout successful',
  });
});

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Get current user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *       401:
 *         description: Authentication required
 */
export const getProfile = asyncHandler(async (req: AuthRequest, res: Response) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    include: {
      profile: true,
      enrollments: {
        include: {
          course: true,
        },
      },
    },
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found',
    });
  }

  res.json({
    success: true,
    data: {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      profile: user.profile,
      isActive: user.isActive,
      lastLogin: user.lastLogin,
      enrollments: user.enrollments,
      createdAt: user.createdAt,
    },
  });
});
