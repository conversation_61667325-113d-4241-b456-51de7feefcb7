{"name": "ngs-lms-backend", "version": "1.0.0", "description": "NGS LMS Backend API Server", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "migrate": "npx prisma migrate dev", "seed": "npx prisma db seed", "generate": "npx prisma generate", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["ngs", "bioinformatics", "lms", "api", "typescript"], "author": "<PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.9.2", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.2", "redis": "^4.6.7", "pg": "^8.11.3", "prisma": "^5.1.1", "@prisma/client": "^5.1.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "compression": "^1.7.4", "rate-limiter-flexible": "^2.4.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.7", "@types/pg": "^8.10.2", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/compression": "^1.7.2", "@types/node": "^20.4.5", "@types/jest": "^29.5.3", "typescript": "^5.1.6", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.6.1", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.45.0"}}