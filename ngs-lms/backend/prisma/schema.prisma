// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User and Profile Models
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String   @unique
  password  String
  role      UserRole @default(STUDENT)
  isActive  Boolean  @default(true)
  lastLogin DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  profile           UserProfile?
  enrollments       Enrollment[]
  submissions       Submission[]
  messages          Message[]        @relation("MessageSender")
  receivedMessages  Message[]        @relation("MessageReceiver")
  practiceEnvironments PracticeEnvironment[]
  progress          UserProgress[]
  instructedCourses Course[]         @relation("CourseInstructor")

  @@map("users")
}

model UserProfile {
  id          String  @id @default(cuid())
  userId      String  @unique
  firstName   String
  lastName    String
  studentId   String?
  institution String
  major       String
  avatar      String?
  bio         String?

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

enum UserRole {
  STUDENT
  INSTRUCTOR
  ADMIN
  GUEST
}

// Course Models
model Course {
  id            String   @id @default(cuid())
  title         String
  titleEn       String?
  description   String
  descriptionEn String?
  code          String   @unique
  credits       Int
  duration      Int      // weeks
  isActive      Boolean  @default(true)
  instructorId  String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  instructor   User           @relation("CourseInstructor", fields: [instructorId], references: [id])
  modules      CourseModule[]
  enrollments  Enrollment[]
  assignments  Assignment[]
  messages     Message[]
  progress     UserProgress[]

  @@map("courses")
}

model CourseModule {
  id            String   @id @default(cuid())
  courseId      String
  title         String
  titleEn       String?
  description   String
  descriptionEn String?
  order         Int
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  course  Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
  lessons Lesson[]

  @@map("course_modules")
}

model Lesson {
  id        String     @id @default(cuid())
  moduleId  String
  title     String
  titleEn   String?
  content   String
  contentEn String?
  type      LessonType
  order     Int
  duration  Int        // minutes
  isActive  Boolean    @default(true)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // Relations
  module    CourseModule    @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  resources LessonResource[]

  @@map("lessons")
}

model LessonResource {
  id       String       @id @default(cuid())
  lessonId String
  title    String
  type     ResourceType
  url      String
  size     Int?
  mimeType String?

  // Relations
  lesson Lesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  @@map("lesson_resources")
}

enum LessonType {
  THEORY
  PRACTICE
  QUIZ
  ASSIGNMENT
}

enum ResourceType {
  VIDEO
  DOCUMENT
  IMAGE
  LINK
  DATASET
}

// Enrollment Model
model Enrollment {
  id         String   @id @default(cuid())
  userId     String
  courseId   String
  isActive   Boolean  @default(true)
  enrolledAt DateTime @default(now())

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@map("enrollments")
}

// Knowledge Base Models
model KnowledgeNode {
  id              String          @id @default(cuid())
  title           String
  titleEn         String?
  content         String
  contentEn       String?
  mediaType       MediaType
  prerequisites   String[]        // Array of knowledge node IDs
  relatedConcepts String[]        // Array of knowledge node IDs
  difficulty      DifficultyLevel
  tags            String[]
  isActive        Boolean         @default(true)
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@map("knowledge_nodes")
}

enum MediaType {
  TEXT
  IMAGE
  VIDEO
  ANIMATION
  INTERACTIVE
}

enum DifficultyLevel {
  BASIC
  INTERMEDIATE
  ADVANCED
}

// Practice Environment Models
model PracticeEnvironment {
  id               String          @id @default(cuid())
  userId           String
  containerId      String?
  sessionId        String          @unique
  status           ContainerStatus @default(CREATING)
  softwareStack    Json            // Store software versions as JSON
  datasetPath      String?
  workingDirectory String          @default("/home/<USER>")
  createdAt        DateTime        @default(now())
  expiresAt        DateTime

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("practice_environments")
}

enum ContainerStatus {
  CREATING
  RUNNING
  STOPPED
  ERROR
  EXPIRED
}

// Assignment Models
model Assignment {
  id          String         @id @default(cuid())
  courseId    String
  title       String
  description String
  type        AssignmentType
  maxScore    Int
  dueDate     DateTime
  isActive    Boolean        @default(true)
  template    Json           // Assignment template as JSON
  rubric      Json           // Grading rubric as JSON
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  course      Course       @relation(fields: [courseId], references: [id], onDelete: Cascade)
  submissions Submission[]

  @@map("assignments")
}

enum AssignmentType {
  PRACTICE_REPORT
  QUIZ
  PROJECT
  PEER_REVIEW
}

model Submission {
  id           String           @id @default(cuid())
  assignmentId String
  userId       String
  content      Json             // Submission content as JSON
  files        SubmissionFile[]
  status       SubmissionStatus @default(DRAFT)
  score        Float?
  feedback     String?
  submittedAt  DateTime         @default(now())
  gradedAt     DateTime?
  gradedBy     String?

  // Relations
  assignment Assignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("submissions")
}

model SubmissionFile {
  id           String   @id @default(cuid())
  submissionId String
  filename     String
  originalName String
  mimeType     String
  size         Int
  path         String
  uploadedAt   DateTime @default(now())

  // Relations
  submission Submission @relation(fields: [submissionId], references: [id], onDelete: Cascade)

  @@map("submission_files")
}

enum SubmissionStatus {
  DRAFT
  SUBMITTED
  GRADING
  GRADED
  RETURNED
}

// Progress Tracking Model
model UserProgress {
  id                   String   @id @default(cuid())
  userId               String
  courseId             String
  completedLessons     String[] // Array of lesson IDs
  completedAssignments String[] // Array of assignment IDs
  totalTimeSpent       Int      @default(0) // minutes
  lastActivity         DateTime @default(now())
  overallProgress      Float    @default(0) // percentage
  moduleProgress       Json     @default("{}") // module progress as JSON
  updatedAt            DateTime @updatedAt

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@map("user_progress")
}

// Communication Models
model Message {
  id         String      @id @default(cuid())
  senderId   String
  receiverId String?
  courseId   String?
  content    String
  type       MessageType
  isRead     Boolean     @default(false)
  createdAt  DateTime    @default(now())

  // Relations
  sender   User    @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver User?   @relation("MessageReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  course   Course? @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@map("messages")
}

enum MessageType {
  DIRECT
  FORUM
  ANNOUNCEMENT
  SYSTEM
}
