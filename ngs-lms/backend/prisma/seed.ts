import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123!@#', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: adminPassword,
      role: 'ADMIN',
      isActive: true,
      profile: {
        create: {
          firstName: '系统',
          lastName: '管理员',
          institution: 'NGS LMS',
          major: '系统管理',
        },
      },
    },
  });

  // Create instructor user
  const instructorPassword = await bcrypt.hash('instructor123!@#', 12);
  const instructor = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'instructor',
      email: '<EMAIL>',
      password: instructorPassword,
      role: 'INSTRUCTOR',
      isActive: true,
      profile: {
        create: {
          firstName: '王',
          lastName: '老师',
          institution: '生物信息学院',
          major: '生物信息学',
        },
      },
    },
  });

  // Create sample student users
  const studentPassword = await bcrypt.hash('student123!@#', 12);
  const student1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'student1',
      email: '<EMAIL>',
      password: studentPassword,
      role: 'STUDENT',
      isActive: true,
      profile: {
        create: {
          firstName: '张',
          lastName: '三',
          studentId: '2023001',
          institution: '生物信息学院',
          major: '生物信息学',
        },
      },
    },
  });

  const student2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'student2',
      email: '<EMAIL>',
      password: studentPassword,
      role: 'STUDENT',
      isActive: true,
      profile: {
        create: {
          firstName: '李',
          lastName: '四',
          studentId: '2023002',
          institution: '生物信息学院',
          major: '计算生物学',
        },
      },
    },
  });

  // Create NGS course
  const course = await prisma.course.upsert({
    where: { code: 'NGS2024' },
    update: {},
    create: {
      title: '高通量测序数据分析',
      titleEn: 'Next-Generation Sequencing Data Analysis',
      description: '本课程将系统介绍高通量测序技术的原理、数据分析流程和实践应用，包括数据质控、基因组组装、注释和系统发育分析等内容。',
      descriptionEn: 'This course provides a comprehensive introduction to next-generation sequencing technologies, data analysis workflows, and practical applications.',
      code: 'NGS2024',
      credits: 3,
      duration: 10, // 10 weeks
      instructorId: instructor.id,
      isActive: true,
    },
  });

  // Create course modules
  const modules = [
    {
      title: '基础知识与环境准备',
      titleEn: 'Fundamentals and Environment Setup',
      description: 'NGS技术概述、HPC环境介绍、数据格式说明',
      descriptionEn: 'Overview of NGS technologies, HPC environment introduction, data format explanation',
      order: 1,
    },
    {
      title: '数据质量控制',
      titleEn: 'Data Quality Control',
      description: '测序数据质量评估、质控工具使用、数据预处理',
      descriptionEn: 'Sequencing data quality assessment, QC tools usage, data preprocessing',
      order: 2,
    },
    {
      title: '基因组Survey分析',
      titleEn: 'Genome Survey Analysis',
      description: 'K-mer分析、基因组特征评估、组装策略制定',
      descriptionEn: 'K-mer analysis, genome feature assessment, assembly strategy planning',
      order: 3,
    },
    {
      title: '基因组组装',
      titleEn: 'Genome Assembly',
      description: '组装算法原理、组装软件使用、组装质量评价',
      descriptionEn: 'Assembly algorithm principles, assembly software usage, assembly quality evaluation',
      order: 4,
    },
    {
      title: '基因组注释',
      titleEn: 'Genome Annotation',
      description: '基因预测、功能注释、注释结果分析',
      descriptionEn: 'Gene prediction, functional annotation, annotation result analysis',
      order: 5,
    },
  ];

  for (const moduleData of modules) {
    await prisma.courseModule.create({
      data: {
        ...moduleData,
        courseId: course.id,
      },
    });
  }

  // Create sample knowledge nodes
  const knowledgeNodes = [
    {
      title: 'NGS技术概述',
      titleEn: 'Overview of NGS Technologies',
      content: '高通量测序（Next-Generation Sequencing, NGS）是一类能够同时对大量DNA分子进行测序的技术...',
      contentEn: 'Next-Generation Sequencing (NGS) refers to a class of technologies that can sequence large numbers of DNA molecules simultaneously...',
      mediaType: 'TEXT',
      difficulty: 'BASIC',
      tags: ['NGS', '测序技术', '基础概念'],
    },
    {
      title: 'Illumina测序原理',
      titleEn: 'Illumina Sequencing Principle',
      content: 'Illumina测序采用边合成边测序（Sequencing by Synthesis, SBS）的原理...',
      contentEn: 'Illumina sequencing uses the Sequencing by Synthesis (SBS) principle...',
      mediaType: 'TEXT',
      difficulty: 'INTERMEDIATE',
      tags: ['Illumina', 'SBS', '测序原理'],
    },
    {
      title: 'FASTQ文件格式',
      titleEn: 'FASTQ File Format',
      content: 'FASTQ格式是存储生物序列及其质量分数的标准格式...',
      contentEn: 'FASTQ format is the standard format for storing biological sequences and their quality scores...',
      mediaType: 'TEXT',
      difficulty: 'BASIC',
      tags: ['FASTQ', '文件格式', '数据结构'],
    },
  ];

  for (const nodeData of knowledgeNodes) {
    await prisma.knowledgeNode.create({
      data: {
        ...nodeData,
        prerequisites: [],
        relatedConcepts: [],
      },
    });
  }

  // Create sample assignments
  const assignment = await prisma.assignment.create({
    data: {
      title: 'NGS数据质控实践报告',
      description: '使用FastQC对提供的测序数据进行质量控制分析，并撰写分析报告',
      type: 'PRACTICE_REPORT',
      maxScore: 100,
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      courseId: course.id,
      template: {
        sections: [
          {
            id: 'objective',
            title: '实验目的',
            description: '描述本次实验的目的和意义',
            isRequired: true,
            type: 'text',
          },
          {
            id: 'methods',
            title: '实验方法',
            description: '详细描述使用的工具和分析步骤',
            isRequired: true,
            type: 'text',
          },
          {
            id: 'results',
            title: '结果分析',
            description: '展示和分析FastQC的输出结果',
            isRequired: true,
            type: 'text',
          },
          {
            id: 'files',
            title: '结果文件',
            description: '上传FastQC生成的HTML报告文件',
            isRequired: true,
            type: 'file_upload',
          },
        ],
        requiredFiles: ['*.html', '*.zip'],
        maxFileSize: 50 * 1024 * 1024, // 50MB
      },
      rubric: {
        criteria: [
          {
            id: 'completeness',
            name: '完整性',
            description: '报告内容是否完整，包含所有必需部分',
            maxPoints: 30,
            levels: [
              { score: 30, description: '内容完整，结构清晰' },
              { score: 20, description: '内容基本完整，结构较清晰' },
              { score: 10, description: '内容不够完整，结构不清晰' },
              { score: 0, description: '内容严重不完整' },
            ],
          },
          {
            id: 'accuracy',
            name: '准确性',
            description: '分析结果和解释是否准确',
            maxPoints: 40,
            levels: [
              { score: 40, description: '分析准确，解释正确' },
              { score: 30, description: '分析基本准确，解释大部分正确' },
              { score: 20, description: '分析部分准确，解释有误' },
              { score: 0, description: '分析错误，解释不当' },
            ],
          },
          {
            id: 'presentation',
            name: '表达能力',
            description: '报告的表达是否清晰、逻辑性强',
            maxPoints: 30,
            levels: [
              { score: 30, description: '表达清晰，逻辑性强' },
              { score: 20, description: '表达较清晰，逻辑性较强' },
              { score: 10, description: '表达不够清晰，逻辑性一般' },
              { score: 0, description: '表达不清晰，逻辑混乱' },
            ],
          },
        ],
        totalPoints: 100,
      },
    },
  });

  // Enroll students in the course
  await prisma.enrollment.createMany({
    data: [
      { userId: student1.id, courseId: course.id },
      { userId: student2.id, courseId: course.id },
    ],
  });

  // Create user progress records
  await prisma.userProgress.createMany({
    data: [
      {
        userId: student1.id,
        courseId: course.id,
        completedLessons: [],
        completedAssignments: [],
        totalTimeSpent: 0,
        overallProgress: 0,
        moduleProgress: {},
      },
      {
        userId: student2.id,
        courseId: course.id,
        completedLessons: [],
        completedAssignments: [],
        totalTimeSpent: 0,
        overallProgress: 0,
        moduleProgress: {},
      },
    ],
  });

  console.log('✅ Database seeding completed successfully!');
  console.log('📊 Created:');
  console.log(`   - Admin user: ${admin.email}`);
  console.log(`   - Instructor: ${instructor.email}`);
  console.log(`   - Students: ${student1.email}, ${student2.email}`);
  console.log(`   - Course: ${course.title} (${course.code})`);
  console.log(`   - Modules: ${modules.length}`);
  console.log(`   - Knowledge nodes: ${knowledgeNodes.length}`);
  console.log(`   - Assignment: ${assignment.title}`);
  console.log('');
  console.log('🔑 Default passwords:');
  console.log('   - Admin: admin123!@#');
  console.log('   - Instructor: instructor123!@#');
  console.log('   - Students: student123!@#');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
