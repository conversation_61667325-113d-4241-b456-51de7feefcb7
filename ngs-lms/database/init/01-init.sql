-- NGS LMS Database Initialization Script

-- Create database if not exists
-- This script is run when the PostgreSQL container starts

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for enums (if needed)
-- Note: Prisma will handle most of the schema creation

-- Create indexes for better performance (will be added after Prisma migration)

-- Insert initial data will be handled by seed scripts

-- Log the initialization
DO $$
BEGIN
    RAISE NOTICE 'NGS LMS Database initialized successfully at %', NOW();
END $$;
