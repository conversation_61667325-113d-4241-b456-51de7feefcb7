# NGS Learning Management System

## 项目概述

基于《高通量测序数据分析》课程设计的综合性在线学习管理系统，支持理论学习、实践操作、结果分析和报告提交的完整教学流程。

## 系统架构

```
ngs-lms/
├── frontend/                 # React前端应用
├── backend/                  # Node.js后端服务
├── database/                 # 数据库脚本和配置
├── docker/                   # Docker配置文件
├── docs/                     # 项目文档
└── scripts/                  # 部署和工具脚本
```

## 技术栈

- **前端**: React.js + TypeScript + Ant Design
- **后端**: Node.js + Express.js + TypeScript
- **数据库**: PostgreSQL + Redis
- **容器化**: Docker + Docker Compose
- **认证**: JWT + bcrypt
- **实时通信**: Socket.io
- **文件存储**: MinIO

## 快速开始

### 环境要求

- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 14+
- Redis 6+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd ngs-lms
```

2. 安装依赖
```bash
# 安装后端依赖
cd backend && npm install

# 安装前端依赖
cd ../frontend && npm install
```

3. 环境配置
```bash
# 复制环境变量文件
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

4. 启动服务
```bash
# 使用Docker Compose启动所有服务
docker-compose up -d

# 或者分别启动
cd backend && npm run dev
cd frontend && npm start
```

## 核心功能模块

### 1. 用户管理系统
- 多角色权限控制（学生、教师、管理员）
- JWT认证和授权
- 用户资料管理

### 2. 课程内容管理
- 结构化知识库
- 多媒体内容支持
- 双语术语解释

### 3. 实践环境模拟
- 容器化HPC环境
- Web终端接口
- 命令验证系统

### 4. 作业提交系统
- 智能报告模板
- 自动化评估
- 文件上传管理

### 5. 实时协作
- 即时通讯
- 论坛讨论
- 进度跟踪

## 开发指南

### 代码规范
- 使用TypeScript进行类型检查
- 遵循ESLint和Prettier配置
- 编写单元测试和集成测试

### API文档
- 使用Swagger生成API文档
- 访问地址: http://localhost:3000/api-docs

### 数据库迁移
```bash
cd backend
npm run migrate
npm run seed
```

## 部署说明

### 开发环境
```bash
docker-compose -f docker-compose.dev.yml up
```

### 生产环境
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

- 项目负责人: 王运生
- 邮箱: <EMAIL>
- 项目地址: https://github.com/your-org/ngs-lms
