# NGS Learning Management System

## 项目概述

基于《高通量测序数据分析》课程设计的综合性在线学习管理系统，支持理论学习、实践操作、结果分析和报告提交的完整教学流程。

## ✨ 主要特性

### 🎓 教学功能
- **结构化课程管理**: 支持多模块课程设计，包含理论学习和实践操作
- **双语支持**: 中英文对照的教学内容和界面
- **进度跟踪**: 实时跟踪学生学习进度和完成情况
- **智能评估**: 自动化作业评分和反馈系统

### 🧪 实践环境
- **容器化HPC环境**: 基于Docker的生物信息学分析环境
- **Web终端**: 浏览器内的SSH终端，支持实时命令执行
- **预装工具**: FastQC, Flye, Prokka, CheckM等NGS分析工具
- **命令验证**: 实时语法检查和参数验证

### 📊 知识管理
- **知识图谱**: 概念关联和学习路径推荐
- **多媒体内容**: 支持文本、图片、视频、动画等多种形式
- **难度分级**: 基础、中级、高级内容分类
- **搜索功能**: 智能搜索和内容推荐

### 👥 用户管理
- **多角色权限**: 学生、教师、管理员不同权限控制
- **个人档案**: 完整的用户资料和学习记录
- **实时通讯**: 师生互动和协作功能

## 🏗️ 系统架构

```
ngs-lms/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/       # 可复用组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务
│   │   ├── store/           # 状态管理
│   │   └── types/           # TypeScript类型定义
├── backend/                  # Node.js后端服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── middleware/      # 中间件
│   │   ├── routes/          # 路由定义
│   │   ├── services/        # 业务逻辑
│   │   └── types/           # 类型定义
│   └── prisma/              # 数据库模式和迁移
├── docker/                   # Docker配置
│   └── practice-env/        # NGS实践环境镜像
├── database/                 # 数据库脚本
├── docs/                     # 项目文档
└── scripts/                  # 部署和工具脚本
```

## 🛠️ 技术栈

### 前端技术
- **React.js 18**: 现代化前端框架
- **TypeScript**: 类型安全的JavaScript
- **Ant Design**: 企业级UI组件库
- **Zustand**: 轻量级状态管理
- **React Query**: 数据获取和缓存
- **Socket.io Client**: 实时通信

### 后端技术
- **Node.js + Express.js**: 高性能Web服务器
- **TypeScript**: 类型安全的服务端开发
- **Prisma**: 现代化数据库ORM
- **JWT**: 安全的用户认证
- **Socket.io**: 实时双向通信
- **Swagger**: API文档自动生成

### 数据存储
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和会话存储
- **MinIO**: 对象存储服务

### 基础设施
- **Docker**: 容器化部署
- **Docker Compose**: 多容器编排
- **Nginx**: 反向代理和负载均衡

## 🚀 快速开始

### 环境要求

- **Node.js**: 18.0+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.0+

### 一键安装

```bash
# 克隆项目
git clone <repository-url>
cd ngs-lms

# 运行安装脚本
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 手动安装

1. **克隆项目**
```bash
git clone <repository-url>
cd ngs-lms
```

2. **安装依赖**
```bash
# 安装根目录依赖
npm install

# 安装后端依赖
cd backend && npm install

# 安装前端依赖
cd ../frontend && npm install
```

3. **环境配置**
```bash
# 复制环境变量文件
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# 编辑配置文件（可选）
nano backend/.env
nano frontend/.env
```

4. **启动服务**
```bash
# 使用Docker Compose启动所有服务
npm run docker:dev

# 或者分别启动服务
npm run dev:backend    # 启动后端服务
npm run dev:frontend   # 启动前端服务
```

## 🌐 访问地址

启动成功后，您可以通过以下地址访问系统：

- **前端应用**: http://localhost:3001
- **后端API**: http://localhost:3000
- **API文档**: http://localhost:3000/api-docs
- **数据库管理**: http://localhost:8080 (Adminer)
- **Redis管理**: http://localhost:8081 (Redis Commander)
- **文件存储**: http://localhost:9001 (MinIO Console)

## 🔑 默认账号

系统预置了以下测试账号：

| 角色 | 邮箱 | 密码 | 说明 |
|------|------|------|------|
| 管理员 | <EMAIL> | admin123!@# | 系统管理员 |
| 教师 | <EMAIL> | instructor123!@# | 课程教师 |
| 学生 | <EMAIL> | student123!@# | 测试学生1 |
| 学生 | <EMAIL> | student123!@# | 测试学生2 |

## 📋 开发工具

### 便捷脚本

```bash
# 开发助手脚本
./scripts/dev.sh start          # 启动所有服务
./scripts/dev.sh stop           # 停止所有服务
./scripts/dev.sh logs backend   # 查看后端日志
./scripts/dev.sh db:reset       # 重置数据库
./scripts/dev.sh shell backend  # 进入后端容器
```

### 代码规范

```bash
# 代码检查
npm run lint                    # 检查代码规范
npm run lint:fix               # 自动修复代码规范

# 测试
npm run test                   # 运行所有测试
npm run test:backend          # 运行后端测试
npm run test:frontend         # 运行前端测试
```

### 数据库操作

```bash
cd backend

# 数据库迁移
npx prisma migrate dev         # 开发环境迁移
npx prisma migrate deploy      # 生产环境迁移

# 数据库种子
npx prisma db seed            # 填充测试数据

# 数据库重置
npx prisma migrate reset      # 重置数据库
```

## 📚 核心功能模块

### 🎓 教学管理
- **课程结构**: 支持多层级课程模块设计
- **学习路径**: 个性化学习进度跟踪
- **资源管理**: 多媒体教学资源上传和管理
- **进度统计**: 实时学习数据分析和可视化

### 🧪 实践环境
- **容器化环境**: 基于Docker的隔离式实践环境
- **预装工具**: 完整的NGS分析工具链
- **实时终端**: Web-based SSH终端支持
- **文件管理**: 在线文件上传、下载和管理

### 📝 作业系统
- **智能模板**: 可配置的作业模板系统
- **自动评分**: 基于规则的自动评分引擎
- **抄袭检测**: 作业相似度检测功能
- **反馈机制**: 详细的评分反馈和建议

### 💬 协作功能
- **实时通讯**: 师生即时消息和讨论
- **论坛系统**: 课程讨论区和问答
- **通知系统**: 重要消息推送和提醒
- **协作编辑**: 多人协作文档编辑

## 🔧 开发指南

### 项目结构

```
src/
├── components/          # 可复用组件
│   ├── auth/           # 认证相关组件
│   ├── common/         # 通用组件
│   ├── layout/         # 布局组件
│   └── practice/       # 实践环境组件
├── pages/              # 页面组件
├── services/           # API服务层
├── store/              # 状态管理
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
└── styles/             # 样式文件
```

### API设计规范

- **RESTful API**: 遵循REST设计原则
- **统一响应格式**: 标准化的API响应结构
- **错误处理**: 完善的错误码和错误信息
- **API版本控制**: 支持API版本管理
- **文档自动生成**: 基于Swagger的API文档

### 数据库设计

- **关系型设计**: 规范化的数据库表结构
- **索引优化**: 关键字段索引优化
- **数据迁移**: 版本化的数据库迁移
- **数据备份**: 自动化数据备份策略

## 🚀 部署指南

### 开发环境

```bash
# 启动开发环境
npm run docker:dev

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 生产环境

详细的生产环境部署指南请参考：[部署文档](docs/DEPLOYMENT.md)

### 性能优化

- **缓存策略**: Redis缓存和浏览器缓存
- **CDN加速**: 静态资源CDN分发
- **数据库优化**: 查询优化和索引调优
- **容器优化**: Docker镜像和容器资源优化

## 🔒 安全特性

- **身份认证**: JWT令牌认证机制
- **权限控制**: 基于角色的访问控制(RBAC)
- **数据加密**: 敏感数据加密存储
- **安全审计**: 操作日志和安全审计
- **防护措施**: XSS、CSRF、SQL注入防护

## 📊 监控和日志

- **应用监控**: 实时性能监控和告警
- **日志管理**: 结构化日志收集和分析
- **错误追踪**: 异常监控和错误报告
- **用户行为**: 用户操作行为分析

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. **Fork项目** 到您的GitHub账户
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送分支** (`git push origin feature/AmazingFeature`)
5. **创建Pull Request**

### 代码贡献规范

- 遵循现有的代码风格和规范
- 添加适当的测试用例
- 更新相关文档
- 确保所有测试通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- **项目负责人**: 王运生
- **邮箱**: <EMAIL>
- **项目地址**: https://github.com/your-org/ngs-lms
- **问题反馈**: https://github.com/your-org/ngs-lms/issues
- **文档中心**: https://docs.ngs-lms.com

## 🙏 致谢

感谢所有为本项目做出贡献的开发者和用户！

特别感谢：
- 生物信息学社区提供的技术支持
- 开源软件项目的启发和帮助
- 测试用户的宝贵反馈和建议
