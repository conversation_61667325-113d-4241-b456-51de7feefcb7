version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:14-alpine
    container_name: ngs-lms-postgres-dev
    environment:
      POSTGRES_DB: ngs_lms_dev
      POSTGRES_USER: ngs_user
      POSTGRES_PASSWORD: ngs_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - ngs-lms-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ngs-lms-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ngs-lms-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: ngs-lms-minio-dev
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - ngs-lms-network
    restart: unless-stopped
    command: server /data --console-address ":9001"

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: ngs-lms-backend-dev
    environment:
      NODE_ENV: development
      DATABASE_URL: ************************************************/ngs_lms_dev
      REDIS_URL: redis://redis:6379
      JWT_SECRET: dev-jwt-secret-key-change-in-production
      JWT_REFRESH_SECRET: dev-refresh-secret-key
      MINIO_ENDPOINT: minio
      MINIO_PORT: 9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      CORS_ORIGIN: http://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./uploads:/app/uploads
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - ngs-lms-network
    depends_on:
      - postgres
      - redis
      - minio
    restart: unless-stopped
    command: npm run dev

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: ngs-lms-frontend-dev
    environment:
      REACT_APP_API_URL: http://localhost:3000
      REACT_APP_WS_URL: ws://localhost:3000
      CHOKIDAR_USEPOLLING: true
    ports:
      - "3001:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - ngs-lms-network
    depends_on:
      - backend
    restart: unless-stopped
    command: npm start

  # NGS Practice Environment Base Image
  ngs-practice-env:
    build:
      context: ./docker/practice-env
      dockerfile: Dockerfile
    image: ngs-lms/practice-env:latest
    container_name: ngs-practice-env-builder
    networks:
      - ngs-lms-network
    profiles:
      - build-only

  # Adminer for Database Management
  adminer:
    image: adminer:latest
    container_name: ngs-lms-adminer-dev
    ports:
      - "8080:8080"
    networks:
      - ngs-lms-network
    depends_on:
      - postgres
    restart: unless-stopped
    profiles:
      - tools

  # Redis Commander for Redis Management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ngs-lms-redis-commander-dev
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - ngs-lms-network
    depends_on:
      - redis
    restart: unless-stopped
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  ngs-lms-network:
    driver: bridge
