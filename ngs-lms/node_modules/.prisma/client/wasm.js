
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  role: 'role',
  isActive: 'isActive',
  lastLogin: 'lastLogin',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  firstName: 'firstName',
  lastName: 'lastName',
  studentId: 'studentId',
  institution: 'institution',
  major: 'major',
  avatar: 'avatar',
  bio: 'bio'
};

exports.Prisma.CourseScalarFieldEnum = {
  id: 'id',
  title: 'title',
  titleEn: 'titleEn',
  description: 'description',
  descriptionEn: 'descriptionEn',
  code: 'code',
  credits: 'credits',
  duration: 'duration',
  isActive: 'isActive',
  instructorId: 'instructorId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CourseModuleScalarFieldEnum = {
  id: 'id',
  courseId: 'courseId',
  title: 'title',
  titleEn: 'titleEn',
  description: 'description',
  descriptionEn: 'descriptionEn',
  order: 'order',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonScalarFieldEnum = {
  id: 'id',
  moduleId: 'moduleId',
  title: 'title',
  titleEn: 'titleEn',
  content: 'content',
  contentEn: 'contentEn',
  type: 'type',
  order: 'order',
  duration: 'duration',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonResourceScalarFieldEnum = {
  id: 'id',
  lessonId: 'lessonId',
  title: 'title',
  type: 'type',
  url: 'url',
  size: 'size',
  mimeType: 'mimeType'
};

exports.Prisma.EnrollmentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  courseId: 'courseId',
  isActive: 'isActive',
  enrolledAt: 'enrolledAt'
};

exports.Prisma.KnowledgeNodeScalarFieldEnum = {
  id: 'id',
  title: 'title',
  titleEn: 'titleEn',
  content: 'content',
  contentEn: 'contentEn',
  mediaType: 'mediaType',
  prerequisites: 'prerequisites',
  relatedConcepts: 'relatedConcepts',
  difficulty: 'difficulty',
  tags: 'tags',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PracticeEnvironmentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  containerId: 'containerId',
  sessionId: 'sessionId',
  status: 'status',
  softwareStack: 'softwareStack',
  datasetPath: 'datasetPath',
  workingDirectory: 'workingDirectory',
  createdAt: 'createdAt',
  expiresAt: 'expiresAt'
};

exports.Prisma.AssignmentScalarFieldEnum = {
  id: 'id',
  courseId: 'courseId',
  title: 'title',
  description: 'description',
  type: 'type',
  maxScore: 'maxScore',
  dueDate: 'dueDate',
  isActive: 'isActive',
  template: 'template',
  rubric: 'rubric',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubmissionScalarFieldEnum = {
  id: 'id',
  assignmentId: 'assignmentId',
  userId: 'userId',
  content: 'content',
  status: 'status',
  score: 'score',
  feedback: 'feedback',
  submittedAt: 'submittedAt',
  gradedAt: 'gradedAt',
  gradedBy: 'gradedBy'
};

exports.Prisma.SubmissionFileScalarFieldEnum = {
  id: 'id',
  submissionId: 'submissionId',
  filename: 'filename',
  originalName: 'originalName',
  mimeType: 'mimeType',
  size: 'size',
  path: 'path',
  uploadedAt: 'uploadedAt'
};

exports.Prisma.UserProgressScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  courseId: 'courseId',
  completedLessons: 'completedLessons',
  completedAssignments: 'completedAssignments',
  totalTimeSpent: 'totalTimeSpent',
  lastActivity: 'lastActivity',
  overallProgress: 'overallProgress',
  moduleProgress: 'moduleProgress',
  updatedAt: 'updatedAt'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  receiverId: 'receiverId',
  courseId: 'courseId',
  content: 'content',
  type: 'type',
  isRead: 'isRead',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  STUDENT: 'STUDENT',
  INSTRUCTOR: 'INSTRUCTOR',
  ADMIN: 'ADMIN',
  GUEST: 'GUEST'
};

exports.LessonType = exports.$Enums.LessonType = {
  THEORY: 'THEORY',
  PRACTICE: 'PRACTICE',
  QUIZ: 'QUIZ',
  ASSIGNMENT: 'ASSIGNMENT'
};

exports.ResourceType = exports.$Enums.ResourceType = {
  VIDEO: 'VIDEO',
  DOCUMENT: 'DOCUMENT',
  IMAGE: 'IMAGE',
  LINK: 'LINK',
  DATASET: 'DATASET'
};

exports.MediaType = exports.$Enums.MediaType = {
  TEXT: 'TEXT',
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  ANIMATION: 'ANIMATION',
  INTERACTIVE: 'INTERACTIVE'
};

exports.DifficultyLevel = exports.$Enums.DifficultyLevel = {
  BASIC: 'BASIC',
  INTERMEDIATE: 'INTERMEDIATE',
  ADVANCED: 'ADVANCED'
};

exports.ContainerStatus = exports.$Enums.ContainerStatus = {
  CREATING: 'CREATING',
  RUNNING: 'RUNNING',
  STOPPED: 'STOPPED',
  ERROR: 'ERROR',
  EXPIRED: 'EXPIRED'
};

exports.AssignmentType = exports.$Enums.AssignmentType = {
  PRACTICE_REPORT: 'PRACTICE_REPORT',
  QUIZ: 'QUIZ',
  PROJECT: 'PROJECT',
  PEER_REVIEW: 'PEER_REVIEW'
};

exports.SubmissionStatus = exports.$Enums.SubmissionStatus = {
  DRAFT: 'DRAFT',
  SUBMITTED: 'SUBMITTED',
  GRADING: 'GRADING',
  GRADED: 'GRADED',
  RETURNED: 'RETURNED'
};

exports.MessageType = exports.$Enums.MessageType = {
  DIRECT: 'DIRECT',
  FORUM: 'FORUM',
  ANNOUNCEMENT: 'ANNOUNCEMENT',
  SYSTEM: 'SYSTEM'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserProfile: 'UserProfile',
  Course: 'Course',
  CourseModule: 'CourseModule',
  Lesson: 'Lesson',
  LessonResource: 'LessonResource',
  Enrollment: 'Enrollment',
  KnowledgeNode: 'KnowledgeNode',
  PracticeEnvironment: 'PracticeEnvironment',
  Assignment: 'Assignment',
  Submission: 'Submission',
  SubmissionFile: 'SubmissionFile',
  UserProgress: 'UserProgress',
  Message: 'Message'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
