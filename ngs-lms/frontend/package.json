{"name": "ngs-lms-frontend", "version": "1.0.0", "description": "NGS LMS Frontend Application", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "antd": "^5.8.0", "react-router-dom": "^6.14.0", "@ant-design/icons": "^5.2.0", "axios": "^1.4.0", "socket.io-client": "^4.7.0", "react-query": "^3.39.0", "zustand": "^4.4.0", "dayjs": "^1.11.0", "react-markdown": "^8.0.0", "prismjs": "^1.29.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "recharts": "^2.7.0", "react-dnd": "^16.0.0", "react-dnd-html5-backend": "^16.0.0"}, "devDependencies": {"@types/prismjs": "^1.26.0", "web-vitals": "^2.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3000"}