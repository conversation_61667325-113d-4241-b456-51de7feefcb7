import React from 'react';
import { Card, Typography, Button } from 'antd';
import { FileTextOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const AssignmentsPage: React.FC = () => {
  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <FileTextOutlined style={{ fontSize: 64, color: '#faad14', marginBottom: 16 }} />
        <Title level={2}>作业提交</Title>
        <Paragraph type="secondary">
          作业提交功能正在开发中，敬请期待...
        </Paragraph>
        <Button type="primary">返回仪表板</Button>
      </div>
    </Card>
  );
};

export default AssignmentsPage;
