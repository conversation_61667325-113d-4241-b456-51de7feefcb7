import React from 'react';
import { Card, Typography, Button } from 'antd';
import { UserOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const ProfilePage: React.FC = () => {
  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <UserOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 16 }} />
        <Title level={2}>个人资料</Title>
        <Paragraph type="secondary">
          个人资料页面正在开发中，敬请期待...
        </Paragraph>
        <Button type="primary">返回仪表板</Button>
      </div>
    </Card>
  );
};

export default ProfilePage;
