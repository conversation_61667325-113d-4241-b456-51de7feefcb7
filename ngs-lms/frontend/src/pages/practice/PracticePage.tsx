import React from 'react';
import { Card, Typography, Button } from 'antd';
import { ExperimentOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const PracticePage: React.FC = () => {
  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <ExperimentOutlined style={{ fontSize: 64, color: '#52c41a', marginBottom: 16 }} />
        <Title level={2}>实践环境</Title>
        <Paragraph type="secondary">
          实践环境功能正在开发中，敬请期待...
        </Paragraph>
        <Button type="primary">返回仪表板</Button>
      </div>
    </Card>
  );
};

export default PracticePage;
