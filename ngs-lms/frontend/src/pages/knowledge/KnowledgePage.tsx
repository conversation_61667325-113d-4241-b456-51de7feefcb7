import React from 'react';
import { Card, Typography, Button } from 'antd';
import { BulbOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const KnowledgePage: React.FC = () => {
  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <BulbOutlined style={{ fontSize: 64, color: '#722ed1', marginBottom: 16 }} />
        <Title level={2}>知识库</Title>
        <Paragraph type="secondary">
          知识库功能正在开发中，敬请期待...
        </Paragraph>
        <Button type="primary">返回仪表板</Button>
      </div>
    </Card>
  );
};

export default KnowledgePage;
