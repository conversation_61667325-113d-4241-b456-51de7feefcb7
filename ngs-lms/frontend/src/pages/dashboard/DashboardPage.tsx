import React from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  Typography,
  List,
  Avatar,
  Button,
  Space,
  Tag,
} from 'antd';
import {
  BookOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '@/store/authStore';

const { Title, Text } = Typography;

const DashboardPage: React.FC = () => {
  const { user } = useAuthStore();

  // Mock data - replace with real API calls
  const stats = {
    enrolledCourses: 3,
    completedLessons: 15,
    pendingAssignments: 2,
    totalStudyTime: 45, // hours
  };

  const recentActivities = [
    {
      id: 1,
      type: 'lesson',
      title: '完成了课程：基因组组装理论',
      time: '2小时前',
      icon: <BookOutlined />,
    },
    {
      id: 2,
      type: 'assignment',
      title: '提交了作业：NGS数据质控报告',
      time: '1天前',
      icon: <FileTextOutlined />,
    },
    {
      id: 3,
      type: 'practice',
      title: '完成了实践：使用FastQC进行质控',
      time: '2天前',
      icon: <ExperimentOutlined />,
    },
  ];

  const upcomingDeadlines = [
    {
      id: 1,
      title: '基因组组装实践报告',
      course: '高通量测序数据分析',
      dueDate: '2024-01-15',
      status: 'urgent',
    },
    {
      id: 2,
      title: '系统发育分析作业',
      course: '高通量测序数据分析',
      dueDate: '2024-01-20',
      status: 'normal',
    },
  ];

  const courseProgress = [
    {
      id: 1,
      title: '高通量测序数据分析',
      progress: 75,
      totalLessons: 20,
      completedLessons: 15,
    },
    {
      id: 2,
      title: '生物信息学基础',
      progress: 100,
      totalLessons: 12,
      completedLessons: 12,
    },
    {
      id: 3,
      title: 'R语言编程',
      progress: 45,
      totalLessons: 16,
      completedLessons: 7,
    },
  ];

  return (
    <div>
      {/* Welcome Section */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          欢迎回来，{user?.profile?.firstName} {user?.profile?.lastName}！
        </Title>
        <Text type="secondary">
          继续您的NGS数据分析学习之旅
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已注册课程"
              value={stats.enrolledCourses}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成课时"
              value={stats.completedLessons}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="待完成作业"
              value={stats.pendingAssignments}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="学习时长"
              value={stats.totalStudyTime}
              suffix="小时"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* Course Progress */}
        <Col xs={24} lg={12}>
          <Card title="课程进度" extra={<Button type="link">查看全部</Button>}>
            <List
              dataSource={courseProgress}
              renderItem={(course) => (
                <List.Item>
                  <div style={{ width: '100%' }}>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      marginBottom: 8 
                    }}>
                      <Text strong>{course.title}</Text>
                      <Text type="secondary">
                        {course.completedLessons}/{course.totalLessons}
                      </Text>
                    </div>
                    <Progress 
                      percent={course.progress} 
                      size="small"
                      status={course.progress === 100 ? 'success' : 'active'}
                    />
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* Recent Activities */}
        <Col xs={24} lg={12}>
          <Card title="最近活动" extra={<Button type="link">查看全部</Button>}>
            <List
              dataSource={recentActivities}
              renderItem={(activity) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={activity.icon} />}
                    title={activity.title}
                    description={activity.time}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* Upcoming Deadlines */}
        <Col xs={24}>
          <Card 
            title="即将到期的作业" 
            extra={<Button type="link">查看全部</Button>}
          >
            <List
              dataSource={upcomingDeadlines}
              renderItem={(deadline) => (
                <List.Item
                  actions={[
                    <Button type="primary" size="small">
                      开始作业
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Avatar icon={<CalendarOutlined />} />}
                    title={
                      <Space>
                        {deadline.title}
                        <Tag color={deadline.status === 'urgent' ? 'red' : 'blue'}>
                          {deadline.status === 'urgent' ? '紧急' : '正常'}
                        </Tag>
                      </Space>
                    }
                    description={
                      <Space direction="vertical" size={0}>
                        <Text type="secondary">{deadline.course}</Text>
                        <Text type="secondary">截止日期：{deadline.dueDate}</Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardPage;
