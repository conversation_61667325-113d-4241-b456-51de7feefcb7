import React from 'react';
import { Card, Typography, Button } from 'antd';
import { BookOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const CourseDetailPage: React.FC = () => {
  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <BookOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 16 }} />
        <Title level={2}>课程详情</Title>
        <Paragraph type="secondary">
          课程详情页面正在开发中，敬请期待...
        </Paragraph>
        <Button type="primary">返回课程列表</Button>
      </div>
    </Card>
  );
};

export default CourseDetailPage;
