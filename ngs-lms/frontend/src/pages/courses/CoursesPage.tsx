import React from 'react';
import { Card, Typography, Button } from 'antd';
import { BookOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const CoursesPage: React.FC = () => {
  return (
    <Card>
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <BookOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 16 }} />
        <Title level={2}>课程管理</Title>
        <Paragraph type="secondary">
          课程管理功能正在开发中，敬请期待...
        </Paragraph>
        <Button type="primary">返回仪表板</Button>
      </div>
    </Card>
  );
};

export default CoursesPage;
