import React from 'react';
import { Spin } from 'antd';

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large';
  tip?: string;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'large', 
  tip = '加载中...', 
  className = 'loading-spinner' 
}) => {
  return (
    <div className={className}>
      <Spin size={size} tip={tip} />
    </div>
  );
};

export default LoadingSpinner;
