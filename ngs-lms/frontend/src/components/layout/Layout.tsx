import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Avatar,
  Dropdown,
  Space,
  Typography,
  Badge,
  Button,
} from 'antd';
import {
  DashboardOutlined,
  BookOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  BulbOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '@/store/authStore';
import { MenuItem } from '@/types';

const { Header, Sider, Content } = AntLayout;
const { Text } = Typography;

const Layout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuthStore();

  // Menu items configuration
  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      label: '仪表板',
      icon: <DashboardOutlined />,
      path: '/dashboard',
    },
    {
      key: 'courses',
      label: '课程管理',
      icon: <BookOutlined />,
      path: '/courses',
    },
    {
      key: 'practice',
      label: '实践环境',
      icon: <ExperimentOutlined />,
      path: '/practice',
    },
    {
      key: 'assignments',
      label: '作业提交',
      icon: <FileTextOutlined />,
      path: '/assignments',
    },
    {
      key: 'knowledge',
      label: '知识库',
      icon: <BulbOutlined />,
      path: '/knowledge',
    },
  ];

  // User dropdown menu
  const userMenuItems = [
    {
      key: 'profile',
      label: '个人资料',
      icon: <UserOutlined />,
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      label: '设置',
      icon: <SettingOutlined />,
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: handleLogout,
    },
  ];

  // Handle menu click
  const handleMenuClick = ({ key }: { key: string }) => {
    const item = menuItems.find(item => item.key === key);
    if (item?.path) {
      navigate(item.path);
    }
  };

  // Handle logout
  async function handleLogout() {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }

  // Get current selected menu key
  const getCurrentKey = () => {
    const path = location.pathname;
    const item = menuItems.find(item => item.path === path);
    return item?.key || 'dashboard';
  };

  return (
    <AntLayout className="app-layout">
      {/* Sidebar */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="app-sider"
        width={256}
      >
        {/* Logo */}
        <div style={{ 
          height: 64, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: collapsed ? 'center' : 'flex-start',
          padding: collapsed ? 0 : '0 24px',
          color: 'white',
          fontSize: 18,
          fontWeight: 'bold',
        }}>
          {collapsed ? 'NGS' : 'NGS LMS'}
        </div>

        {/* Navigation Menu */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[getCurrentKey()]}
          onClick={handleMenuClick}
          items={menuItems.map(item => ({
            key: item.key,
            icon: item.icon,
            label: item.label,
          }))}
        />
      </Sider>

      <AntLayout>
        {/* Header */}
        <Header className="app-header">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
                color: 'white',
              }}
            />
          </div>

          <Space size="middle">
            {/* Notifications */}
            <Badge count={5} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ color: 'white' }}
              />
            </Badge>

            {/* User Info */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Space style={{ cursor: 'pointer', color: 'white' }}>
                <Avatar
                  size="small"
                  src={user?.profile?.avatar}
                  icon={<UserOutlined />}
                />
                <Text style={{ color: 'white' }}>
                  {user?.profile?.firstName} {user?.profile?.lastName}
                </Text>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* Main Content */}
        <Content className="app-content">
          <Outlet />
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
