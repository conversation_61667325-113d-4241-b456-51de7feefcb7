// User Types
export enum UserRole {
  STUDENT = 'student',
  INSTRUCTOR = 'instructor',
  ADMIN = 'admin',
  GUEST = 'guest'
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: UserRole;
  profile: UserProfile;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  studentId?: string;
  institution: string;
  major: string;
  avatar?: string;
  bio?: string;
}

// Auth Types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  studentId?: string;
  institution: string;
  major: string;
  role?: UserRole;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
}

export interface AuthResponse {
  user: User;
  tokens: AuthTokens;
}

// Course Types
export interface Course {
  id: string;
  title: string;
  titleEn?: string;
  description: string;
  descriptionEn?: string;
  code: string;
  credits: number;
  duration: number;
  isActive: boolean;
  instructorId: string;
  instructor?: User;
  createdAt: string;
  updatedAt: string;
}

export interface CourseModule {
  id: string;
  courseId: string;
  title: string;
  titleEn?: string;
  description: string;
  descriptionEn?: string;
  order: number;
  isActive: boolean;
  lessons: Lesson[];
}

export interface Lesson {
  id: string;
  moduleId: string;
  title: string;
  titleEn?: string;
  content: string;
  contentEn?: string;
  type: LessonType;
  order: number;
  duration: number;
  isActive: boolean;
  resources: LessonResource[];
}

export enum LessonType {
  THEORY = 'theory',
  PRACTICE = 'practice',
  QUIZ = 'quiz',
  ASSIGNMENT = 'assignment'
}

export interface LessonResource {
  id: string;
  lessonId: string;
  title: string;
  type: ResourceType;
  url: string;
  size?: number;
  mimeType?: string;
}

export enum ResourceType {
  VIDEO = 'video',
  DOCUMENT = 'document',
  IMAGE = 'image',
  LINK = 'link',
  DATASET = 'dataset'
}

// Practice Environment Types
export interface PracticeEnvironment {
  id: string;
  userId: string;
  containerId?: string;
  sessionId: string;
  status: ContainerStatus;
  softwareStack: SoftwareStack;
  datasetPath?: string;
  workingDirectory: string;
  createdAt: string;
  expiresAt: string;
}

export enum ContainerStatus {
  CREATING = 'creating',
  RUNNING = 'running',
  STOPPED = 'stopped',
  ERROR = 'error',
  EXPIRED = 'expired'
}

export interface SoftwareStack {
  conda?: string;
  fastqc?: string;
  jellyfish?: string;
  flye?: string;
  prokka?: string;
  quast?: string;
  checkm?: string;
  fastani?: string;
  gtotree?: string;
  mashtree?: string;
}

// Command Types
export interface CommandExecution {
  command: string;
  workingDirectory?: string;
  timeout?: number;
}

export interface CommandResult {
  output: string;
  error?: string;
  exitCode: number;
  executionTime: number;
}

// Assignment Types
export interface Assignment {
  id: string;
  courseId: string;
  title: string;
  description: string;
  type: AssignmentType;
  maxScore: number;
  dueDate: string;
  isActive: boolean;
  template: AssignmentTemplate;
  rubric: GradingRubric;
  createdAt: string;
  updatedAt: string;
}

export enum AssignmentType {
  PRACTICE_REPORT = 'practice_report',
  QUIZ = 'quiz',
  PROJECT = 'project',
  PEER_REVIEW = 'peer_review'
}

export interface AssignmentTemplate {
  sections: TemplateSection[];
  requiredFiles: string[];
  maxFileSize: number;
}

export interface TemplateSection {
  id: string;
  title: string;
  description: string;
  isRequired: boolean;
  type: SectionType;
  placeholder?: string;
}

export enum SectionType {
  TEXT = 'text',
  CODE = 'code',
  FILE_UPLOAD = 'file_upload',
  MULTIPLE_CHOICE = 'multiple_choice',
  CHECKBOX = 'checkbox'
}

export interface GradingRubric {
  criteria: RubricCriterion[];
  totalPoints: number;
}

export interface RubricCriterion {
  id: string;
  name: string;
  description: string;
  maxPoints: number;
  levels: RubricLevel[];
}

export interface RubricLevel {
  score: number;
  description: string;
}

// Submission Types
export interface Submission {
  id: string;
  assignmentId: string;
  userId: string;
  content: SubmissionContent;
  files: SubmissionFile[];
  status: SubmissionStatus;
  score?: number;
  feedback?: string;
  submittedAt: string;
  gradedAt?: string;
  gradedBy?: string;
}

export interface SubmissionContent {
  sections: { [sectionId: string]: any };
  metadata: SubmissionMetadata;
}

export interface SubmissionMetadata {
  commandHistory: CommandRecord[];
  timeSpent: number;
  environment: string;
  softwareVersions: { [tool: string]: string };
}

export interface CommandRecord {
  command: string;
  timestamp: string;
  exitCode: number;
  output?: string;
  error?: string;
}

export interface SubmissionFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  path: string;
  uploadedAt: string;
}

export enum SubmissionStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  GRADING = 'grading',
  GRADED = 'graded',
  RETURNED = 'returned'
}

// Progress Types
export interface UserProgress {
  userId: string;
  courseId: string;
  completedLessons: string[];
  completedAssignments: string[];
  totalTimeSpent: number;
  lastActivity: string;
  overallProgress: number;
  moduleProgress: { [moduleId: string]: number };
}

// Knowledge Base Types
export interface KnowledgeNode {
  id: string;
  title: string;
  titleEn?: string;
  content: string;
  contentEn?: string;
  mediaType: MediaType;
  prerequisites: string[];
  relatedConcepts: string[];
  difficulty: DifficultyLevel;
  tags: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export enum MediaType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  ANIMATION = 'animation',
  INTERACTIVE = 'interactive'
}

export enum DifficultyLevel {
  BASIC = 'basic',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// UI Types
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  path?: string;
}

export interface BreadcrumbItem {
  title: string;
  path?: string;
}

// Socket Types
export interface SocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

// File Types
export interface FileUpload {
  file: File;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  response?: any;
}
