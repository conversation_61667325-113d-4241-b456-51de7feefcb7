/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  font-size: 14px;
  line-height: 1.5715;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
}

#root {
  height: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}

/* Layout styles */
.app-layout {
  min-height: 100vh;
}

.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: #001529;
  color: white;
}

.app-logo {
  display: flex;
  align-items: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
  text-decoration: none;
}

.app-logo:hover {
  color: white;
}

.app-sider {
  background: #001529;
}

.app-content {
  margin: 24px;
  padding: 24px;
  background: white;
  border-radius: 6px;
  min-height: calc(100vh - 112px);
}

/* Terminal styles */
.terminal-container {
  background: #000;
  border-radius: 6px;
  padding: 16px;
  height: 400px;
  overflow: hidden;
}

.terminal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #333;
  color: #fff;
}

.terminal-title {
  font-size: 14px;
  font-weight: 500;
}

.terminal-controls {
  display: flex;
  gap: 8px;
}

.terminal-control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
}

.terminal-control.close {
  background: #ff5f56;
}

.terminal-control.minimize {
  background: #ffbd2e;
}

.terminal-control.maximize {
  background: #27ca3f;
}

/* Code editor styles */
.code-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.code-editor-header {
  background: #fafafa;
  padding: 8px 16px;
  border-bottom: 1px solid #d9d9d9;
  font-size: 12px;
  color: #666;
}

/* Knowledge graph styles */
.knowledge-graph {
  width: 100%;
  height: 500px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

/* Practice environment styles */
.practice-environment {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px);
}

.practice-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
}

.practice-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.practice-sidebar {
  width: 300px;
  border-right: 1px solid #d9d9d9;
  background: white;
  overflow-y: auto;
}

.practice-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Assignment styles */
.assignment-card {
  margin-bottom: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.assignment-header {
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
}

.assignment-content {
  padding: 16px;
}

.assignment-footer {
  padding: 12px 16px;
  background: #fafafa;
  border-top: 1px solid #d9d9d9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Progress styles */
.progress-card {
  text-align: center;
  padding: 24px;
}

.progress-circle {
  margin-bottom: 16px;
}

.progress-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.progress-description {
  color: #666;
  font-size: 14px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .app-content {
    margin: 16px;
    padding: 16px;
  }
  
  .practice-content {
    flex-direction: column;
  }
  
  .practice-sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #d9d9d9;
  }
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 24px;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
