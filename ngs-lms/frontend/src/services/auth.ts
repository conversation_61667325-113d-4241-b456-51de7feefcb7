import { apiClient } from './api';
import { LoginCredentials, RegisterData, AuthResponse, User, ApiResponse } from '@/types';

export const authService = {
  // Login user
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await apiClient.post<ApiResponse<AuthResponse>>('/auth/login', credentials);
    return response.data.data!;
  },

  // Register user
  register: async (userData: RegisterData): Promise<AuthResponse> => {
    const response = await apiClient.post<ApiResponse<AuthResponse>>('/auth/register', userData);
    return response.data.data!;
  },

  // Logout user
  logout: async (): Promise<void> => {
    await apiClient.post('/auth/logout');
  },

  // Get current user profile
  getProfile: async (): Promise<User> => {
    const response = await apiClient.get<ApiResponse<User>>('/auth/me');
    return response.data.data!;
  },

  // Refresh token
  refreshToken: async (refreshToken: string): Promise<{ tokens: any }> => {
    const response = await apiClient.post<ApiResponse<{ tokens: any }>>('/auth/refresh', {
      refreshToken,
    });
    return response.data.data!;
  },

  // Change password
  changePassword: async (userId: string, passwords: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<void> => {
    await apiClient.put(`/users/${userId}/password`, passwords);
  },

  // Update profile
  updateProfile: async (userId: string, profileData: Partial<User['profile']>): Promise<User> => {
    const response = await apiClient.put<ApiResponse<User>>(`/users/${userId}`, profileData);
    return response.data.data!;
  },

  // Upload avatar
  uploadAvatar: async (userId: string, file: File): Promise<{ avatar: string }> => {
    const formData = new FormData();
    formData.append('avatar', file);
    
    const response = await apiClient.upload<ApiResponse<{ avatar: string }>>(
      `/users/${userId}/avatar`,
      formData
    );
    return response.data.data!;
  },
};
