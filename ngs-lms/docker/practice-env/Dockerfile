# NGS Practice Environment Docker Image
FROM ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV CONDA_DIR=/opt/conda
ENV PATH=$CONDA_DIR/bin:$PATH
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    git \
    vim \
    nano \
    htop \
    tree \
    unzip \
    gzip \
    tar \
    bzip2 \
    xz-utils \
    build-essential \
    cmake \
    zlib1g-dev \
    libbz2-dev \
    liblzma-dev \
    libncurses5-dev \
    libncursesw5-dev \
    libssl-dev \
    libffi-dev \
    libsqlite3-dev \
    libreadline-dev \
    tk-dev \
    python3 \
    python3-pip \
    python3-dev \
    r-base \
    r-base-dev \
    default-jre \
    openssh-server \
    sudo \
    && rm -rf /var/lib/apt/lists/*

# Install Miniconda
RUN wget --quiet https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O ~/miniconda.sh && \
    /bin/bash ~/miniconda.sh -b -p $CONDA_DIR && \
    rm ~/miniconda.sh && \
    $CONDA_DIR/bin/conda clean -tipsy

# Configure conda
RUN conda config --add channels defaults && \
    conda config --add channels bioconda && \
    conda config --add channels conda-forge && \
    conda config --set channel_priority strict

# Create NGS course environment
RUN conda create -n ngs_course python=3.9 -y

# Activate environment and install NGS tools
RUN echo "source activate ngs_course" > ~/.bashrc
ENV CONDA_DEFAULT_ENV=ngs_course
ENV CONDA_PREFIX=$CONDA_DIR/envs/ngs_course
ENV PATH=$CONDA_PREFIX/bin:$PATH

# Install bioinformatics tools
RUN conda install -n ngs_course -c bioconda -y \
    fastqc=0.12.1 \
    jellyfish=2.3.0 \
    flye=2.9.2 \
    prokka=1.14.6 \
    quast=5.2.0 \
    checkm-genome=1.2.2 \
    fastani=1.33 \
    gtotree=1.8.1 \
    mashtree=1.4.6 \
    trimmomatic=0.39 \
    fastp=0.23.4 \
    multiqc=1.14 \
    samtools=1.17 \
    bcftools=1.17 \
    bwa=0.7.17 \
    minimap2=2.24 \
    seqkit=2.4.0 \
    && conda clean -afy

# Install additional Python packages
RUN pip install --no-cache-dir \
    numpy \
    pandas \
    matplotlib \
    seaborn \
    scipy \
    scikit-learn \
    biopython \
    jupyter \
    jupyterlab

# Create student user
RUN useradd -m -s /bin/bash student && \
    echo "student:student" | chpasswd && \
    usermod -aG sudo student && \
    echo "student ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Create working directories
RUN mkdir -p /home/<USER>/data /home/<USER>/results /home/<USER>/scripts && \
    chown -R student:student /home/<USER>

# Copy sample data and scripts
COPY sample_data/ /home/<USER>/data/
COPY scripts/ /home/<USER>/scripts/
RUN chown -R student:student /home/<USER>/data /home/<USER>/scripts

# Set up SSH
RUN mkdir /var/run/sshd && \
    echo 'PermitRootLogin no' >> /etc/ssh/sshd_config && \
    echo 'PasswordAuthentication yes' >> /etc/ssh/sshd_config && \
    echo 'PubkeyAuthentication yes' >> /etc/ssh/sshd_config

# Configure conda for student user
USER student
WORKDIR /home/<USER>

# Initialize conda for student
RUN conda init bash && \
    echo "conda activate ngs_course" >> ~/.bashrc

# Create useful aliases
RUN echo 'alias ll="ls -la"' >> ~/.bashrc && \
    echo 'alias la="ls -A"' >> ~/.bashrc && \
    echo 'alias l="ls -CF"' >> ~/.bashrc && \
    echo 'alias ..="cd .."' >> ~/.bashrc && \
    echo 'alias ...="cd ../.."' >> ~/.bashrc

# Set up environment variables
ENV HOME=/home/<USER>
ENV USER=student

# Switch back to root for final setup
USER root

# Expose SSH port
EXPOSE 22

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD service ssh status || exit 1

# Start SSH service
CMD ["/usr/sbin/sshd", "-D"]
