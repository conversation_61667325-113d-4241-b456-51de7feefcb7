#!/bin/bash

# NGS Course Environment Setup Script
# This script sets up the practice environment for NGS data analysis

echo "Setting up NGS practice environment..."

# Create directory structure
mkdir -p ~/practice/{raw_data,qc_results,assembly,annotation,analysis}
mkdir -p ~/practice/scripts
mkdir -p ~/practice/logs

# Set permissions
chmod 755 ~/practice
chmod 755 ~/practice/*

# Create sample configuration files
cat > ~/practice/config.yaml << 'EOF'
# NGS Analysis Configuration
project_name: "ngs_practice"
data_dir: "~/practice/raw_data"
output_dir: "~/practice/results"
threads: 4
memory: "8G"

# Quality Control
fastqc:
  enabled: true
  output_dir: "~/practice/qc_results"

# Assembly
flye:
  genome_size: "5m"
  min_overlap: 5000
  iterations: 1

# Annotation
prokka:
  kingdom: "Bacteria"
  genus: "Escherichia"
  species: "coli"
EOF

# Create useful scripts
cat > ~/practice/scripts/run_fastqc.sh << 'EOF'
#!/bin/bash
# FastQC Quality Control Script

INPUT_DIR=${1:-~/practice/raw_data}
OUTPUT_DIR=${2:-~/practice/qc_results}

echo "Running FastQC on files in $INPUT_DIR"
echo "Output will be saved to $OUTPUT_DIR"

mkdir -p $OUTPUT_DIR

for file in $INPUT_DIR/*.fastq $INPUT_DIR/*.fastq.gz $INPUT_DIR/*.fq $INPUT_DIR/*.fq.gz; do
    if [ -f "$file" ]; then
        echo "Processing: $file"
        fastqc "$file" -o "$OUTPUT_DIR"
    fi
done

echo "FastQC analysis complete!"
echo "Results saved in: $OUTPUT_DIR"
EOF

cat > ~/practice/scripts/run_assembly.sh << 'EOF'
#!/bin/bash
# Genome Assembly Script using Flye

INPUT_FILE=${1}
OUTPUT_DIR=${2:-~/practice/assembly}
GENOME_SIZE=${3:-5m}

if [ -z "$INPUT_FILE" ]; then
    echo "Usage: $0 <input_file> [output_dir] [genome_size]"
    echo "Example: $0 reads.fastq ~/practice/assembly 5m"
    exit 1
fi

echo "Starting genome assembly with Flye..."
echo "Input file: $INPUT_FILE"
echo "Output directory: $OUTPUT_DIR"
echo "Estimated genome size: $GENOME_SIZE"

mkdir -p $OUTPUT_DIR

flye --nano-raw "$INPUT_FILE" \
     --out-dir "$OUTPUT_DIR" \
     --genome-size "$GENOME_SIZE" \
     --threads 4

echo "Assembly complete!"
echo "Results saved in: $OUTPUT_DIR"
EOF

cat > ~/practice/scripts/run_annotation.sh << 'EOF'
#!/bin/bash
# Genome Annotation Script using Prokka

INPUT_FASTA=${1}
OUTPUT_DIR=${2:-~/practice/annotation}
PREFIX=${3:-sample}

if [ -z "$INPUT_FASTA" ]; then
    echo "Usage: $0 <input_fasta> [output_dir] [prefix]"
    echo "Example: $0 assembly.fasta ~/practice/annotation sample"
    exit 1
fi

echo "Starting genome annotation with Prokka..."
echo "Input FASTA: $INPUT_FASTA"
echo "Output directory: $OUTPUT_DIR"
echo "Prefix: $PREFIX"

mkdir -p $OUTPUT_DIR

prokka "$INPUT_FASTA" \
       --outdir "$OUTPUT_DIR" \
       --prefix "$PREFIX" \
       --kingdom Bacteria \
       --cpus 4 \
       --force

echo "Annotation complete!"
echo "Results saved in: $OUTPUT_DIR"
EOF

# Make scripts executable
chmod +x ~/practice/scripts/*.sh

# Create sample README
cat > ~/practice/README.md << 'EOF'
# NGS Practice Environment

Welcome to the NGS data analysis practice environment!

## Directory Structure

- `raw_data/`: Raw sequencing data files
- `qc_results/`: Quality control results
- `assembly/`: Genome assembly results
- `annotation/`: Genome annotation results
- `analysis/`: Additional analysis results
- `scripts/`: Useful analysis scripts
- `logs/`: Log files

## Available Tools

### Quality Control
- FastQC: Quality assessment of sequencing data
- MultiQC: Aggregate QC reports

### Assembly
- Flye: Long-read genome assembler
- Jellyfish: K-mer counting

### Annotation
- Prokka: Prokaryotic genome annotation
- CheckM: Genome quality assessment

### Analysis
- FastANI: Average nucleotide identity calculation
- GToTree: Phylogenetic tree construction
- MashTree: Fast phylogenetic tree construction

## Quick Start

1. Upload your data to the `raw_data/` directory
2. Run quality control: `./scripts/run_fastqc.sh`
3. Perform assembly: `./scripts/run_assembly.sh your_reads.fastq`
4. Annotate genome: `./scripts/run_annotation.sh assembly.fasta`

## Useful Commands

```bash
# Check tool versions
fastqc --version
flye --version
prokka --version

# List available conda environments
conda env list

# Activate NGS environment
conda activate ngs_course

# Check system resources
htop
df -h
```

## Getting Help

- Use `man <command>` for manual pages
- Use `<command> --help` for command help
- Check tool documentation online

Happy analyzing!
EOF

echo "Environment setup complete!"
echo "Practice directory created at: ~/practice"
echo "Sample scripts available in: ~/practice/scripts"
echo "Read the README.md file for more information"

# Display directory structure
echo ""
echo "Directory structure:"
tree ~/practice 2>/dev/null || ls -la ~/practice
