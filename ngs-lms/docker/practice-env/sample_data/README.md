# Sample Data for NGS Practice

This directory contains sample datasets for practicing NGS data analysis.

## Available Datasets

### 1. E. coli Sample Dataset
- **File**: `ecoli_sample.fastq.gz`
- **Description**: Simulated E. coli sequencing data
- **Technology**: Oxford Nanopore (simulated)
- **Coverage**: ~50x
- **Genome Size**: ~4.6 Mb
- **Use Case**: Basic assembly and annotation practice

### 2. Small Bacterial Genome
- **File**: `small_bacteria.fastq.gz`
- **Description**: Small bacterial genome for quick testing
- **Technology**: PacBio (simulated)
- **Coverage**: ~30x
- **Genome Size**: ~2 Mb
- **Use Case**: Quick assembly testing

### 3. Quality Control Test Data
- **Files**: `good_quality.fastq`, `poor_quality.fastq`
- **Description**: Samples with different quality profiles
- **Use Case**: FastQC practice and quality assessment

## Usage Instructions

1. **Copy data to your working directory**:
   ```bash
   cp /home/<USER>/data/* ~/practice/raw_data/
   ```

2. **Extract compressed files**:
   ```bash
   gunzip ~/practice/raw_data/*.gz
   ```

3. **Run quality control**:
   ```bash
   cd ~/practice
   ./scripts/run_fastqc.sh
   ```

4. **Perform assembly**:
   ```bash
   ./scripts/run_assembly.sh raw_data/ecoli_sample.fastq
   ```

5. **Annotate genome**:
   ```bash
   ./scripts/run_annotation.sh assembly/assembly.fasta
   ```

## Expected Results

### FastQC Results
- Per base sequence quality plots
- Sequence length distribution
- GC content analysis
- Adapter content detection

### Assembly Results
- Assembled contigs in FASTA format
- Assembly statistics (N50, total length, etc.)
- Assembly graph visualization

### Annotation Results
- Gene predictions in GFF format
- Protein sequences in FASTA format
- Functional annotations
- Summary statistics

## Tips for Practice

1. **Start with small datasets** to understand the workflow
2. **Compare results** between different parameter settings
3. **Visualize results** using available tools
4. **Document your analysis** steps and observations
5. **Experiment with different tools** for the same task

## Troubleshooting

### Common Issues
- **Out of memory**: Reduce thread count or use smaller datasets
- **File not found**: Check file paths and permissions
- **Tool not found**: Ensure conda environment is activated

### Getting Help
```bash
# Check if tools are available
which fastqc
which flye
which prokka

# Activate conda environment
conda activate ngs_course

# Check available memory and disk space
free -h
df -h
```

## Additional Resources

- [FastQC Documentation](https://www.bioinformatics.babraham.ac.uk/projects/fastqc/)
- [Flye Documentation](https://github.com/fenderglass/Flye)
- [Prokka Documentation](https://github.com/tseemann/prokka)

Happy learning!
