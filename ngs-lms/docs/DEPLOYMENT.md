# NGS LMS 部署指南

本文档详细介绍了如何部署NGS学习管理系统。

## 系统要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上（推荐16GB）
- **存储**: 100GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Docker支持的Linux发行版
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Node.js**: 18+ (开发环境)
- **Git**: 2.0+

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd ngs-lms
```

### 2. 运行安装脚本
```bash
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 3. 启动开发环境
```bash
npm run docker:dev
```

### 4. 访问应用
- 前端: http://localhost:3001
- 后端API: http://localhost:3000
- API文档: http://localhost:3000/api-docs

## 详细部署步骤

### 开发环境部署

#### 1. 环境准备
```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### 2. 配置环境变量
```bash
# 复制环境变量文件
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# 编辑配置文件
nano backend/.env
nano frontend/.env
```

#### 3. 安装依赖
```bash
# 安装项目依赖
npm run install:all
```

#### 4. 构建Docker镜像
```bash
# 构建所有镜像
docker-compose -f docker-compose.dev.yml build

# 构建NGS实践环境镜像
docker build -t ngs-lms/practice-env:latest docker/practice-env/
```

#### 5. 启动服务
```bash
# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps
```

#### 6. 初始化数据库
```bash
cd backend
npx prisma migrate dev
npx prisma db seed
cd ..
```

### 生产环境部署

#### 1. 创建生产环境配置
```bash
cp docker-compose.dev.yml docker-compose.prod.yml
```

编辑 `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: ngs_lms_prod
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: always

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: always

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${DB_USER}:${DB_PASSWORD}@postgres:5432/ngs_lms_prod
      JWT_SECRET: ${JWT_SECRET}
    depends_on:
      - postgres
      - redis
    restart: always

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    depends_on:
      - backend
    restart: always

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: always
```

#### 2. 配置Nginx
创建 `nginx/nginx.conf`:
```nginx
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:3000;
    }

    upstream frontend {
        server frontend:3000;
    }

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}
```

#### 3. 设置环境变量
创建 `.env.prod`:
```bash
DB_USER=ngs_user_prod
DB_PASSWORD=secure_password_here
JWT_SECRET=very_secure_jwt_secret_here
JWT_REFRESH_SECRET=very_secure_refresh_secret_here
```

#### 4. 启动生产环境
```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

## 监控和维护

### 日志管理
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend

# 查看最近的日志
docker-compose logs --tail=100 backend
```

### 数据备份
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U ngs_user ngs_lms_prod > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份文件存储
tar -czf files_backup_$(date +%Y%m%d_%H%M%S).tar.gz uploads/
```

### 性能监控
```bash
# 查看容器资源使用情况
docker stats

# 查看系统资源
htop
df -h
```

### 更新部署
```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker-compose build

# 重启服务
docker-compose up -d
```

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库容器状态
docker-compose ps postgres

# 查看数据库日志
docker-compose logs postgres

# 重启数据库
docker-compose restart postgres
```

#### 2. 前端无法访问后端API
- 检查CORS配置
- 验证API URL配置
- 检查网络连接

#### 3. 实践环境创建失败
```bash
# 检查Docker守护进程
sudo systemctl status docker

# 检查镜像是否存在
docker images | grep ngs-lms/practice-env

# 重新构建实践环境镜像
docker build -t ngs-lms/practice-env:latest docker/practice-env/
```

#### 4. 内存不足
- 增加系统内存
- 调整Docker内存限制
- 优化应用配置

### 性能优化

#### 1. 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_enrollments_user_course ON enrollments(user_id, course_id);
CREATE INDEX idx_submissions_assignment ON submissions(assignment_id);
```

#### 2. 缓存优化
- 配置Redis缓存策略
- 实施CDN加速
- 启用浏览器缓存

#### 3. 容器优化
```yaml
# 在docker-compose.yml中设置资源限制
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

## 安全配置

### 1. 防火墙设置
```bash
# 只开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 2. SSL证书配置
```bash
# 使用Let's Encrypt获取免费SSL证书
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

### 3. 定期安全更新
```bash
# 更新系统包
sudo apt update && sudo apt upgrade

# 更新Docker镜像
docker-compose pull
docker-compose up -d
```

## 扩展部署

### 水平扩展
```yaml
# 在docker-compose.yml中配置多个实例
services:
  backend:
    deploy:
      replicas: 3
    
  nginx:
    # 配置负载均衡
```

### 集群部署
- 使用Kubernetes进行容器编排
- 配置数据库主从复制
- 实施分布式文件存储

## 支持和维护

如需技术支持，请联系：
- 邮箱: <EMAIL>
- 文档: https://docs.ngs-lms.com
- 问题反馈: https://github.com/your-org/ngs-lms/issues
