# 《高通量测序数据分析》详细教案 (Detailed Lesson Plan)

**课程名称:** 高通量测序数据分析 (Data Analysis of NGS)
**课程代码:** B392J03000
**总学时:** 2周 (集中实践)
**面向专业:** 生物信息学
**先修课程:** 基因组学，生物信息学，R语言编程，Linux基础与应用

**教学理念:** 理论指导实践，实践深化理论。强调服务器环境下的实际操作。

**考核方式:** 实践报告 (100%)，要求记录详细步骤、参数、原理及结果解释。

---

## 第一周

**总体目标:** 熟悉HPC环境，掌握基础数据处理、基因组Survey和组装流程。

**Day 1: 课程导入与环境准备**
*   **上午 (理论 + 演示)**
    *   **主题:** 课程介绍与高通量测序技术概览
    *   **内容:**
        *   课程目标、安排、考核方式说明。
        *   DNA测序技术发展简史 (Sanger 到 NGS 各平台)。
        *   主流NGS平台原理简介 (Illumina, PacBio, ONT)。
        *   NGS在生命科学中的应用。
    *   **对应目标:** 1.1
*   **下午 (理论 + 演示 + 实践)**
    *   **主题:** 高性能计算集群(HPC)入门
    *   **内容:**
        *   HPC基本概念。
        *   登录服务器 (SSH)。
        *   Linux常用命令复习 (cd, ls, mkdir, cp, mv, rm, head, tail, less, grep)。
        *   文件传输 (scp/sftp)。
        *   作业调度系统介绍 (以SGE为例: qsub, qstat, qdel)。
        *   编写简单的提交脚本。
    *   **对应目标:** 1.3
    *   **实践:** 登录服务器，练习基本命令，提交一个简单的测试脚本。

**Day 2: 环境管理与数据初步**
*   **上午 (理论 + 实践)**
    *   **主题:** Conda 环境管理
    *   **内容:**
        *   为什么需要环境管理。
        *   Miniconda/Anaconda 安装与配置 (初始化shell)。
        *   Conda 基本命令 (create, activate, deactivate, install, list, search, remove, env list)。
        *   Channel 的概念 (conda-forge, bioconda)。
        *   创建课程专用环境，安装基础工具 (e.g., fastqc)。
    *   **对应目标:** 1.3
    *   **实践:** 安装Miniconda，配置channel，创建环境，安装软件。
*   **下午 (理论 + 演示)**
    *   **主题:** NGS数据格式与质控
    *   **内容:**
        *   FASTQ 格式详解 (Identifier, Sequence, +, Quality)。
        *   FASTA 格式。
        *   测序数据质量控制 (QC) 的重要性。
        *   常见QC指标解读 (碱基质量分布, GC含量分布, N含量, 接头污染等)。
        *   QC常用工具介绍 (FastQC)。
    *   **对应目标:** 1.2
    *   **演示:** 使用FastQC分析示例数据，解读报告。

**Day 3: 基因组Survey**
*   **上午 (理论)**
    *   **主题:** K-mer 基本概念与应用
    *   **内容:**
        *   K-mer 定义与原理。
        *   K-mer 频数分布曲线解读。
        *   利用K-mer估算基因组大小、杂合度、重复序列比例。
    *   **对应目标:** 1.2
*   **下午 (实践)**
    *   **主题:** K-mer 分析实战
    *   **工具:** Jellyfish / KMC
    *   **内容:**
        *   使用Jellyfish/KMC对二代测序数据进行K-mer计数。
        *   生成K-mer频数分布表。
        *   (可选) 使用 GCE 或 GenomeScope 等工具进行基因组特征估算。
    *   **对应目标:** 1.2, 1.3
    *   **实践:** 运行K-mer计数，尝试估算基因组特征。

**Day 4: 基因组组装理论与准备**
*   **上午 (理论)**
    *   **主题:** 基因组组装策略与三代测序数据
    *   **内容:**
        *   基因组组装的基本概念 (Contig, Scaffold, Gap)。
        *   主要组装策略: OLC (Overlap-Layout-Consensus), De Bruijn Graph。
        *   三代测序 (PacBio HiFi, ONT) 数据特点 (长读长、错误率模型)。
        *   适用于三代数据的组装软件介绍 (Flye, Canu, Hifiasm等)。
        *   组装中的挑战 (重复序列、高杂合区域)。
    *   **对应目标:** 2.1, 3.1, 3.3, 3.2
*   **下午 (实践准备)**
    *   **主题:** 组装实战准备
    *   **内容:**
        *   下载或准备用于组装的三代测序数据 (示例数据)。
        *   检查数据完整性。
        *   安装基因组组装软件 (e.g., Flye) 到Conda环境。
        *   准备组装提交脚本。
    *   **对应目标:** 1.3

**Day 5: 基因组组装实战**
*   **全天 (实践 + 监控)**
    *   **主题:** 运行基因组组装
    *   **内容:**
        *   使用选定的组装软件 (e.g., Flye) 提交组装任务到HPC。
        *   监控作业运行状态 (qstat)。
        *   查看日志文件，初步排查错误。
        *   (根据数据量和计算资源，组装可能需要较长时间)
    *   **对应目标:** 2.1, 1.3
    *   **实践:** 提交组装任务，学习监控和管理HPC作业。

---

## 第二周

**总体目标:** 掌握基因组注释、评价方法，并进行物种鉴定和系统发育分析。

**Day 6: 组装后处理与基因组注释**
*   **上午 (理论 + 检查)**
    *   **主题:** 组装结果检查与注释理论
    *   **内容:**
        *   检查组装任务是否完成，查看主要输出文件 (assembly.fasta)。
        *   组装基本统计指标 (N50, L50, 总长度等) - 概念。
        *   (可选) 组装后Polish的概念 (使用二代数据或原始三代数据校正)。
        *   基因组注释的目标和流程。
        *   细菌/古菌基因组注释常用工具 (Prokka)。
    *   **对应目标:** 2.1, 2.2, 3.1, 3.2
*   **下午 (实践)**
    *   **主题:** 细菌基因组注释实战
    *   **工具:** Prokka
    *   **内容:**
        *   安装 Prokka 及其依赖。
        *   使用 Prokka 对组装好的基因组进行注释。
        *   理解 Prokka 的主要输出文件 (gff, ffn, faa, fna, tsv)。
    *   **对应目标:** 2.2, 1.3
    *   **实践:** 运行Prokka注释，浏览输出结果。

**Day 7: 基因组质量评价**
*   **上午 (理论)**
    *   **主题:** 基因组组装与注释质量评价
    *   **内容:**
        *   评价指标:
            *   组装连续性 (N50/L50, 最大Contig等)。
            *   完整性 (BUSCO, CheckM - 基于保守单拷贝基因)。
            *   准确性 (与参考基因组比对 - Quast; Polish前后的比较)。
            *   污染评估 (CheckM)。
        *   常用评价工具介绍 (Quast, BUSCO, CheckM)。
    *   **对应目标:** 1.2, 2.3
*   **下午 (实践)**
    *   **主题:** 基因组评价实战
    *   **工具:** Quast, CheckM
    *   **内容:**
        *   安装 Quast 和 CheckM。
        *   使用 Quast 评估基因组组装统计数据 (可包含与参考基因组的比对)。
        *   使用 CheckM 评估基因组的完整性和污染度。
        *   解读 Quast 和 CheckM 的报告。
    *   **对应目标:** 2.3, 1.3
    *   **实践:** 运行Quast和CheckM，分析结果报告。

**Day 8: 物种鉴定**
*   **上午 (理论)**
    *   **主题:** 基于基因组的物种鉴定
    *   **内容:**
        *   物种鉴定的挑战与方法。
        *   平均核苷酸一致性 (Average Nucleotide Identity, ANI) 的概念和原理。
        *   ANI 的计算方法和常用工具 (JSpeciesWS - Web, FastANI - 命令行)。
        *   ANI 在细菌/古菌物种界定中的阈值。
    *   **对应目标:** 3.3
*   **下午 (实践)**
    *   **主题:** ANI 计算实战
    *   **工具:** FastANI (命令行)
    *   **内容:**
        *   安装 FastANI。
        *   准备查询基因组和参考基因组列表。
        *   运行 FastANI 计算两两基因组间的 ANI 值。
        *   解读输出结果，判断物种归属。
    *   **对应目标:** 3.3, 1.3
    *   **实践:** 下载参考基因组，运行FastANI进行物种鉴定。

**Day 9: 系统发育分析**
*   **上午 (理论 + 实践准备)**
    *   **主题:** 基于全基因组的系统发育树构建
    *   **内容:**
        *   系统发育分析基本概念。
        *   基于标记基因 vs 基于全基因组的系统发育分析。
        *   常用工具介绍:
            *   NCBI Datasets: 命令行下载基因组数据。
            *   GToTree: 基于单拷贝基因构建系统发育树。
            *   Mashtree: 基于 Mash distance 快速构建近似系统发育树。
    *   **对应目标:** 3.1, 3.2
    *   **实践准备:** 安装 NCBI Datasets, GToTree, Mashtree。
*   **下午 (实践)**
    *   **主题:** 系统发育树构建实战
    *   **工具:** NCBI Datasets, GToTree / Mashtree
    *   **内容:**
        *   使用 `datasets` 下载相关物种的基因组。
        *   使用 GToTree (推荐) 或 Mashtree 构建系统发育树。
        *   (可选) 使用 FigTree 或 iTOL 可视化树文件。
    *   **对应目标:** 3.1, 3.2, 1.3
    *   **实践:** 下载数据，运行GToTree/Mashtree，尝试可视化结果。

**Day 10: 总结与报告撰写**
*   **上午 (复习 + Q&A)**
    *   **主题:** 课程回顾与答疑
    *   **内容:**
        *   梳理整个分析流程。
        *   回顾关键概念和工具。
        *   解答学生在实践中遇到的问题。
*   **下午 (指导 + 实践)**
    *   **主题:** 实践报告撰写指导与实践
    *   **内容:**
        *   讲解实践报告的要求 (格式、内容、深度)。
        *   强调记录命令、参数选择依据、结果解释的重要性。
        *   学生开始整理笔记，撰写报告。
        *   教师提供个别指导。
    *   **对应目标:** 1.2, 2.1, 2.2, 2.3, 3.1, 3.2 (通过报告体现)

---
**教师:** 王运生, 张莹钧, 陈渊
**实践环境:** 校内HPC集群，学生个人电脑 (通过SSH访问集群)
**主要软件:** Conda, FastQC, Jellyfish/KMC, Flye/Canu, Prokka, Quast, CheckM, FastANI, NCBI Datasets, GToTree/Mashtree, (FigTree/iTOL)