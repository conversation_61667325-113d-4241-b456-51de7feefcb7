# 《高通量测序数据分析》实践指导手册 (Practical Guide)

---

# 《高通量测序数据分析》在线学习管理系统设计方案

## 系统概述

基于现有的NGS数据分析课程材料，设计一个综合性的在线学习管理系统(LMS)，支持理论学习、实践操作、结果分析和报告提交的完整教学流程。

## 详细设计方案

### 1. 系统架构设计

#### 1.1 技术栈推荐
- **前端**: React.js + TypeScript + Ant Design
- **后端**: Node.js + Express.js + TypeScript
- **数据库**: PostgreSQL (主数据库) + Redis (缓存)
- **文件存储**: MinIO (对象存储) + NFS (共享文件系统)
- **容器化**: Docker + Kubernetes
- **认证**: JWT + OAuth 2.0
- **实时通信**: Socket.io
- **监控**: Prometheus + Grafana

#### 1.2 微服务架构
```
├── 用户管理服务 (User Management Service)
├── 课程内容服务 (Content Management Service)
├── 实践环境服务 (Practice Environment Service)
├── 作业提交服务 (Assignment Submission Service)
├── 评估反馈服务 (Assessment & Feedback Service)
├── 通信协作服务 (Communication Service)
└── 系统监控服务 (Monitoring Service)
```

---

**欢迎来到《高通量测序数据分析》实践课程！**

本手册旨在指导你完成课程中的各项实践操作。请仔细阅读每一步说明，并在你的终端（连接到HPC集群）中执行相应的命令。

**重要提示:**
*   所有命令均假设在Linux环境下执行。
*   `$` 或 `#` 开头的行表示命令行提示符，后面的内容是需要输入的命令。不要输入提示符本身。
*   `<placeholder>` 表示你需要替换成实际值的内容 (例如, `<your_username>`, `<path_to_data>`)。
*   务必记录你运行的每一个命令、使用的参数以及参数选择的理由。这对撰写最终的实践报告至关重要。
*   部分命令可能需要较长时间运行，请耐心等待或使用作业调度系统提交到后台运行。
*   示例数据路径和文件名需要根据实际情况修改。

---

## 实践一: HPC集群入门与环境准备

### 1.1 登录HPC集群

使用SSH客户端 (如终端、PuTTY、MobaXterm) 登录到指定的HPC集群。

```bash
ssh <your_username>@<cluster_address>
# 输入你的密码
```

### 1.2 Linux 基础命令回顾

练习以下常用命令：
*   `pwd`: 显示当前工作目录。
*   `ls -lht`: 查看当前目录内容 (详细列表、人类可读大小、按时间排序)。
*   `mkdir <new_directory_name>`: 创建新目录 (例如: `mkdir ngs_analysis`)。
*   `cd <directory_name>`: 切换目录 (例如: `cd ngs_analysis`)。
*   `cp <source_file> <destination>`: 复制文件。
*   `mv <source> <destination>`: 移动或重命名文件/目录。
*   `rm <file_name>`: 删除文件 (谨慎使用!)。
*   `rm -r <directory_name>`: 删除目录及其内容 (非常谨慎!)。
*   `head <file_name>`: 查看文件开头几行。
*   `tail <file_name>`: 查看文件末尾几行。
*   `less <file_name>`: 分页查看文件内容 (按 `q` 退出)。
*   `grep <pattern> <file_name>`: 在文件中搜索模式。

### 1.3 作业调度系统 (以SGE为例)

*   **查看队列状态:** `qstat`
*   **查看节点负载:** `qhost`
*   **提交作业:**
    *   创建一个脚本文件 (例如 `myjob.sh`):
      ```bash
      #!/bin/bash
      #$ -N my_test_job  # 作业名称
      #$ -cwd           # 在当前工作目录运行
      #$ -q <queue_name> # 指定队列 (询问管理员)
      #$ -pe <parallel_environment> <num_cores> # 请求并行环境和核心数 (如果需要)
      #$ -l vf=<memory_amount>G,p=<priority> # 请求内存和其他资源

      echo "Job started on $(hostname) at $(date)"
      # 在这里写下你的命令, 例如:
      sleep 60
      echo "Job finished at $(date)"
      ```
    *   提交脚本: `qsub myjob.sh`
*   **查看作业详情:** `qstat -j <job_id>`
*   **删除作业:** `qdel <job_id>`

### 1.4 Conda 环境管理

*   **安装 Miniconda (如果尚未安装):**
    *   下载安装脚本: `wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh`
    *   运行安装脚本: `bash Miniconda3-latest-Linux-x86_64.sh` (按照提示操作，建议接受默认设置，并在最后允许 `conda init`)
    *   关闭并重新打开终端，或运行 `source ~/.bashrc` 使配置生效。
*   **配置 Channels (推荐):**
    ```bash
    conda config --add channels defaults
    conda config --add channels bioconda
    conda config --add channels conda-forge
    conda config --set channel_priority strict
    ```
*   **创建课程环境:**
    ```bash
    conda create -n ngs_course python=3.9 -y
    ```
*   **激活环境:**
    ```bash
    conda activate ngs_course
    ```
    *(之后的所有软件安装和运行都在此环境下进行)*
*   **安装基础工具 (示例):**
    ```bash
    conda install -c bioconda fastqc -y
    ```
*   **查看已安装的包:** `conda list`
*   **退出环境:** `conda deactivate`

---

## 实践二: NGS数据质控 (FastQC)

*   **激活环境:** `conda activate ngs_course`
*   **获取数据:** 假设你的原始测序数据 (FASTQ格式) 位于 `<data_directory>`，文件名为 `reads_1.fq.gz` 和 `reads_2.fq.gz` (如果是双端测序)。
*   **运行 FastQC:**
    ```bash
    # 创建输出目录
    mkdir fastqc_results

    # 对每个文件运行FastQC
    fastqc <data_directory>/reads_1.fq.gz -o fastqc_results/
    fastqc <data_directory>/reads_2.fq.gz -o fastqc_results/
    # 如果文件很多，可以使用通配符: fastqc <data_directory>/*.fq.gz -o fastqc_results/
    ```
*   **查看报告:**
    *   FastQC 会在 `fastqc_results/` 目录下生成 HTML 报告文件。
    *   将这些 HTML 文件下载到你的本地电脑 (使用 `scp` 或 SFTP 客户端)。
    *   用浏览器打开 HTML 文件，仔细查看各项指标 (Per base sequence quality, Per sequence quality scores, Per base sequence content, GC content, Adapter Content 等)。
    *   根据报告判断数据质量，是否存在接头污染等问题。*(注意：实际分析中可能需要根据QC结果进行数据过滤，如使用Trimmomatic或fastp，本实践暂不涉及)*

---

## 实践三: 基因组Survey (K-mer分析)

*   **激活环境:** `conda activate ngs_course`
*   **安装 K-mer 计数工具 (以 Jellyfish 为例):**
    ```bash
    conda install -c bioconda jellyfish -y
    ```
*   **获取数据:** 使用二代测序数据 (通常是 Illumina 数据)，假设为 `reads_1.fq.gz` 和 `reads_2.fq.gz`。
*   **运行 Jellyfish 计数:**
    ```bash
    # 选择合适的 K 值 (通常 17, 19, 21, ... 31 之间)
    K=21

    # 计数 (假设是gzip压缩文件)
    # -m: K值
    # -s: hash大小 (根据基因组大小和K值估算，例如 1G, 5G)
    # -t: 线程数
    # -C: 处理双端文件
    # --gzip: 输入是gzip压缩
    jellyfish count -m $K -s 5G -t 8 -C --gzip -o kmer_counts.jf <data_directory>/reads_*.fq.gz

    # 生成频数分布直方图数据
    jellyfish histo -t 8 kmer_counts.jf > kmer_histo.tsv
    ```
*   **解读结果:**
    *   查看 `kmer_histo.tsv` 文件。第一列是 K-mer 出现次数 (频数)，第二列是具有该频数的 K-mer 种类数。
    *   绘制 K-mer 频数分布图 (可以使用 R 或 Python)。
    *   观察主峰位置 (对应杂合 K-mer 深度)，估算基因组大小 (总 K-mer 数 / 主峰深度)。
    *   观察是否有重复序列峰、高杂合峰等。
    *   (可选) 使用 GenomeScope (在线或本地) 分析 `kmer_histo.tsv` 文件，获取更详细的基因组特征估计 (大小、杂合度、重复比例)。

---

## 实践四: 基因组组装 (以 Flye 为例)

*   **激活环境:** `conda activate ngs_course`
*   **安装 Flye:**
    ```bash
    conda install -c bioconda flye -y
    ```
*   **获取数据:** 使用三代测序数据 (PacBio HiFi 或 ONT)，假设文件为 `long_reads.fq.gz`。
*   **准备组装脚本 (例如 `flye_job.sh`):**
    ```bash
    #!/bin/bash
    #$ -N flye_assembly
    #$ -cwd
    #$ -q <queue_name>
    #$ -pe <parallel_environment> 16 # 请求16个核心
    #$ -l vf=64G,p=1 # 请求64G内存

    conda activate ngs_course # 确保在正确的环境中运行

    # 输入的长读长文件
    READS="<path_to_data>/long_reads.fq.gz"
    # 输出目录
    OUT_DIR="flye_output"
    # 线程数 (与请求的核心数匹配)
    THREADS=16
    # 预估基因组大小 (可选但推荐, 例如 5m 表示 5Mb)
    GENOME_SIZE="5m"
    # 测序数据类型: --pacbio-raw, --pacbio-hifi, --nano-raw, --nano-hq
    READ_TYPE="--pacbio-hifi" # 根据你的数据类型修改

    echo "Starting Flye assembly at $(date)"

    flye ${READ_TYPE} ${READS} \
         --out-dir ${OUT_DIR} \
         --genome-size ${GENOME_SIZE} \
         --threads ${THREADS}

    echo "Flye assembly finished at $(date)"
    ```
*   **提交组装任务:**
    ```bash
    qsub flye_job.sh
    ```
*   **监控与结果:**
    *   使用 `qstat` 监控作业状态。
    *   组装完成后，进入 `flye_output` 目录。
    *   主要结果文件是 `assembly.fasta` (组装得到的 Contigs)。
    *   查看 `flye.log` 文件获取详细运行信息和统计数据。
    *   初步评估组装结果: 查看 `assembly_info.txt` 文件中的统计信息 (总长度, Contig 数量, N50 等)。

---

## 实践五: 基因组注释 (Prokka)

*   **激活环境:** `conda activate ngs_course`
*   **安装 Prokka:**
    ```bash
    conda install -c bioconda prokka -y
    # Prokka 依赖较多，首次安装可能需要较长时间
    # 可能需要安装一些数据库，根据 Prokka 提示操作
    # prokka --setupdb
    ```
*   **获取数据:** 使用上一步组装得到的 `assembly.fasta` 文件。
*   **运行 Prokka:**
    ```bash
    # 输入的组装文件
    ASSEMBLY="flye_output/assembly.fasta"
    # 输出目录
    OUT_DIR="prokka_annotation"
    # 物种属名 (可选, 提高注释准确性)
    GENUS="GenusName" # 替换成你的目标属名
    # 物种名 (可选)
    SPECIES="speciesname" # 替换成你的目标种名
    # 菌株名 (可选)
    STRAIN="strain123"
    # 指定遗传密码子表 (细菌通常是 11)
    KINGDOM="Bacteria" # 或 Archaea, Viruses
    GCODE=11
    # 线程数
    THREADS=8

    prokka --outdir ${OUT_DIR} --prefix ${STRAIN} \
           --genus ${GENUS} --species ${SPECIES} --strain ${STRAIN} \
           --kingdom ${KINGDOM} --gcode ${GCODE} \
           --cpus ${THREADS} \
           ${ASSEMBLY}

    echo "Prokka annotation finished at $(date)"
    ```
    *(如果是在HPC上运行，建议写成提交脚本)*
*   **解读结果:**
    *   进入 `prokka_annotation` 目录。
    *   `.gff`: GFF3 格式的注释文件，包含基因位置、功能等信息。
    *   `.fna`: 注释后的基因组序列 (FASTA)。
    *   `.ffn`: 核酸序列 (FASTA)，包含所有预测出的 CDS 和 RNA。
    *   `.faa`: 氨基酸序列 (FASTA)，包含所有预测出的蛋白质。
    *   `.tsv`: 制表符分隔的注释摘要信息。
    *   `.txt`: 注释运行的统计报告。
    *   浏览这些文件，了解基因组包含哪些基因、功能等。

---

## 实践六: 基因组质量评价 (Quast & CheckM)

*   **激活环境:** `conda activate ngs_course`
*   **安装 Quast 和 CheckM:**
    ```bash
    conda install -c bioconda quast checkm-genome -y
    # CheckM 可能需要下载数据库，根据提示操作
    # checkm data update (可能需要较长时间和空间)
    ```
*   **获取数据:**
    *   组装结果: `flye_output/assembly.fasta`
    *   (可选) 参考基因组: `<reference_genome.fasta>`
    *   (可选) 注释文件: `prokka_annotation/*.gff`

### 6.1 Quast 评估组装统计

```bash
# 输入的组装文件
ASSEMBLY="flye_output/assembly.fasta"
# 输出目录
OUT_DIR="quast_results"
# 线程数
THREADS=8
# (可选) 参考基因组
REF_GENOME="<path_to_reference>/reference.fasta"
# (可选) 注释文件 (GFF格式)
GFF_FILE="prokka_annotation/*.gff" # 使用Prokka的GFF

# 运行 Quast (带参考基因组和注释)
quast.py ${ASSEMBLY} \
         -o ${OUT_DIR} \
         -r ${REF_GENOME} \
         -g ${GFF_FILE} \
         --threads ${THREADS}

# 运行 Quast (不带参考基因组)
# quast.py ${ASSEMBLY} -o ${OUT_DIR} --threads ${THREADS}
```
*   **解读结果:**
    *   进入 `quast_results` 目录。
    *   查看 `report.html` (下载到本地用浏览器打开) 或 `report.tsv`。
    *   关注 N50, L50, 总长度, 最大Contig长度, Contig数量等组装连续性指标。
    *   如果提供了参考基因组，关注比对相关的指标 (Genome fraction %, Mismatches per 100 kbp, Indels per 100 kbp) 和 Misassemblies。
    *   如果提供了GFF，关注找到的基因数量。

### 6.2 CheckM 评估完整性和污染度

```bash
# 输入的组装文件所在的目录 (CheckM需要目录作为输入)
ASSEMBLY_DIR="flye_output/" # 假设 assembly.fasta 在此目录下
# 输出目录
OUT_DIR="checkm_results"
# 线程数
THREADS=8

# 运行 CheckM (lineage_wf 工作流)
checkm lineage_wf ${ASSEMBLY_DIR} ${OUT_DIR} \
       --tab_table \
       -t ${THREADS} \
       -x fasta # 指定输入文件扩展名

# (如果知道物种分类，可以指定更具体的标记基因集，例如:)
# checkm taxon_list # 查看可用分类单元
# checkm lineage_wf -t 16 -x fa --reduced_tree --tab_table -f checkm.tsv Bacteria prokka_annotation/ checkm_output
```
*   **解读结果:**
    *   进入 `checkm_results` 目录。
    *   查看 `lineage.ms` 文件 (或用 `--tab_table` 生成的 TSV 文件)。
    *   关注 **Completeness** (完整性) 和 **Contamination** (污染度)。
    *   理想的基因组应该具有高完整性 (接近 100%) 和低污染度 (接近 0%)。
    *   Strain heterogeneity (菌株异质性) 指标也值得关注。

---

## 实践七: 基于全基因组的物种鉴定 (FastANI)

*   **激活环境:** `conda activate ngs_course`
*   **安装 FastANI:**
    ```bash
    conda install -c bioconda fastani -y
    ```
*   **获取数据:**
    *   你的查询基因组: `flye_output/assembly.fasta`
    *   参考基因组: 从 NCBI 或其他数据库下载已知物种的基因组 FASTA 文件。将它们放在一个目录下，例如 `reference_genomes/`。
    *   创建一个包含所有参考基因组文件路径的列表文件 `ref_list.txt` (每行一个文件路径)。
      ```bash
      # 例如，在 reference_genomes/ 目录下运行:
      find $(pwd)/reference_genomes -name "*.fasta" > ref_list.txt
      # 或者手动创建:
      # /path/to/reference_genomes/speciesA.fasta
      # /path/to/reference_genomes/speciesB.fasta
      # ...
      ```
*   **运行 FastANI:**
    ```bash
    # 查询基因组
    QUERY_GENOME="flye_output/assembly.fasta"
    # 参考基因组列表
    REF_LIST="ref_list.txt"
    # 输出文件
    OUTPUT_FILE="ani_results.txt"
    # 线程数
    THREADS=8

    fastani --query ${QUERY_GENOME} --refList ${REF_LIST} -o ${OUTPUT_FILE} -t ${THREADS}
    ```
*   **解读结果:**
    *   查看 `ani_results.txt` 文件。
    *   文件包含五列: 查询基因组, 参考基因组, ANI值 (%), 比对上的片段数, 总片段数。
    *   找到 ANI 值最高的参考基因组。
    *   通常，ANI > 95-96% 被认为是同种的标准。根据这个阈值判断你的组装基因组属于哪个物种。

---

## 实践八: 基于全基因组构建系统进化树

*   **激活环境:** `conda activate ngs_course`
*   **安装工具:**
    ```bash
    # NCBI Datasets 命令行工具
    # (安装方法可能随版本变化，请参考官方文档)
    # 通常需要下载二进制文件或使用包管理器
    # 例如:
    # wget <link_to_datasets_binary>
    # chmod +x datasets
    # ./datasets version # 检查安装

    # GToTree
    conda install -c bioconda gtotree -y

    # Mashtree (可选, 快速方法)
    conda install -c bioconda mashtree -y

    # 可视化工具 (可选, 本地安装)
    # FigTree, iTOL (在线)
    ```

### 8.1 使用 NCBI Datasets 下载基因组

*   确定你要分析的物种范围 (例如，与你的目标物种相关的几个近缘种)。
*   查找这些物种的 NCBI Assembly Accession 号 (例如 GCF_xxxxxxxx.x)。
*   创建一个包含这些 Accession 号的列表文件 `accessions.txt` (每行一个)。
*   下载基因组:
    ```bash
    datasets download genome accession --inputfile accessions.txt --include genome --filename genomes.zip
    unzip genomes.zip -d downloaded_genomes
    # 整理下载的文件，通常在 downloaded_genomes/ncbi_dataset/data/ 目录下找到 FASTA 文件 (.fna)
    # 将你的组装基因组 (assembly.fasta) 也复制到包含下载基因组的目录中
    ```

### 8.2 使用 GToTree 构建系统发育树 (基于单拷贝基因)

```bash
# 包含所有基因组 FASTA 文件的目录
GENOMES_DIR="path/to/all/genomes/" # 包含你的组装和下载的基因组
# 选择一个合适的单拷贝基因集 (HMM模型)
# 例如，细菌: Bacteria.hmm (GToTree自带)
HMM_SET="Bacteria.hmm"
# 输出文件前缀
OUT_PREFIX="phylogeny"
# 线程数
THREADS=16

# 运行 GToTree
GToTree -A ${GENOMES_DIR}/*.fna -H ${HMM_SET} -o ${OUT_PREFIX} -j ${THREADS}
# GToTree 会自动完成基因查找、比对、过滤、建树等步骤
```
*   **解读结果:**
    *   主要输出文件是 `${OUT_PREFIX}.tre` (Newick 格式的树文件)。
    *   将此文件下载到本地，使用 FigTree 或 iTOL 等软件进行可视化和美化。
    *   观察你的基因组在进化树上的位置，确定其与其他物种的亲缘关系。

### 8.3 (可选) 使用 Mashtree 快速构建系统发育树

```bash
# 包含所有基因组 FASTA 文件的目录
GENOMES_DIR="path/to/all/genomes/"
# 输出树文件
OUT_TREE="mashtree_output.tre"
# 线程数
THREADS=16

mashtree --num-threads ${THREADS} ${GENOMES_DIR}/*.fna > ${OUT_TREE}
```
*   **解读结果:**
    *   同样得到 Newick 格式的树文件 `${OUT_TREE}`。
    *   Mashtree 基于 Mash distance，速度快，但可能不如基于多基因比对的方法精确，适用于快速预览。

---

## 实践九: 实践报告撰写

*   **整理笔记:** 回顾你在每个实践步骤中运行的命令、使用的参数、遇到的问题及解决方法。
*   **报告结构:**
    *   **封面:** 课程名称、姓名、学号、日期等。
    *   **引言:** 简述高通量测序数据分析的重要性及本次实践的目标。
    *   **方法:**
        *   详细描述每个分析步骤 (QC, Survey, Assembly, Annotation, Evaluation, Identification, Phylogeny)。
        *   列出使用的主要软件及其版本。
        *   **关键:** 清晰记录每一步运行的核心命令和所使用的参数。解释为什么选择这些参数 (例如 K-mer 值的选择、Flye 的数据类型参数、Prokka 的遗传密码子等)。
    *   **结果与讨论:**
        *   展示每个步骤的关键结果 (例如 FastQC 报告截图、K-mer 分布图、组装统计表、注释基因数量、Quast/CheckM 评估结果、ANI 值、系统发育树图)。
        *   对结果进行必要的描述、说明和解释。例如：
            *   数据质量如何？是否需要过滤？
            *   基因组 Survey 揭示了哪些基因组特征？
            *   组装结果的连续性和完整性如何？
            *   注释发现了哪些重要的基因或通路？
            *   基因组评价结果是否达到标准？
            *   你的样本被鉴定为哪个物种？依据是什么？
            *   系统发育树揭示了怎样的进化关系？
        *   讨论分析过程中遇到的问题以及如何解决的。
        *   (可选) 对比不同参数或软件可能产生的影响。
    *   **结论:** 总结本次实践的主要发现和学习收获。
    *   **参考文献:** (如果引用了外部资料)
*   **格式要求:** 报告清晰、格式规范、图表清晰。

**祝你实践顺利，学有所获！**