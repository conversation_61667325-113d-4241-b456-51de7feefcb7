# 《高通量测序数据分析》教案 

**课程名称:** 高通量测序数据分析 (Data Analysis of NGS)
**课程代码:** B392J03000
**总学时:** 2周 (集中实践)
**面向专业:** 生物信息学
**先修课程:** 基因组学，生物信息学，R语言编程，Linux基础与应用

**教学理念:** 理论指导实践，实践深化理论。强调服务器环境下的实际操作。

**考核方式:** 实践报告 (100%)，要求记录详细步骤、参数、原理及结果解释。

---

## 第一周

**总体目标:** 熟悉HPC环境，掌握基础数据处理、基因组Survey和组装流程。

**Day 1: 课程导入与环境准备**
*   **上午 (理论 + 演示)**
    *   **主题:** 课程介绍与高通量测序技术概览
    *   **内容:**
        *   课程目标、安排、考核方式说明。
        *   DNA测序技术发展简史 (Sanger 到 NGS 各平台)。
        *   主流NGS平台原理简介 (Illumina, PacBio, ONT)。
        *   NGS在生命科学中的应用。
    *   **对应目标:** 1.1
*   **下午 (理论 + 演示 + 实践)**
    *   **主题:** 高性能计算集群(HPC)入门
    *   **内容:**
        *   HPC基本概念。
        *   登录服务器 (SSH)。
        *   Linux常用命令复习 (cd, ls, mkdir, cp, mv, rm, head, tail, less, grep)。
        *   文件传输 (scp/sftp)。
        *   作业调度系统介绍 (以SGE为例: qsub, qstat, qdel)。
        *   编写简单的提交脚本。
    *   **对应目标:** 1.3
    *   **实践:** 登录服务器，练习基本命令，提交一个简单的测试脚本。

**Day 2: 环境管理与数据初步**
*   **上午 (理论 + 实践)**
    *   **主题:** Conda 环境管理
    *   **内容:**
        *   为什么需要环境管理。
        *   Miniconda/Anaconda 安装与配置 (初始化shell)。
        *   Conda 基本命令 (create, activate, deactivate, install, list, search, remove, env list)。
        *   Channel 的概念 (conda-forge, bioconda)。
        *   创建课程专用环境，安装基础工具 (e.g., fastqc)。
    *   **对应目标:** 1.3
    *   **实践:** 安装Miniconda，配置channel，创建环境，安装软件。
*   **下午 (理论 + 演示)**
    *   **主题:** NGS数据格式与质控
    *   **内容:**
        *   FASTQ 格式详解 (Identifier, Sequence, +, Quality)。
        *   FASTA 格式。
        *   测序数据质量控制 (QC) 的重要性。
        *   常见QC指标解读 (碱基质量分布, GC含量分布, N含量, 接头污染等)。
        *   QC常用工具介绍 (FastQC)。
    *   **对应目标:** 1.2
    *   **演示:** 使用FastQC分析示例数据，解读报告。

**Day 3: 基因组Survey**
*   **上午 (理论)**
    *   **主题:** K-mer 基本概念与应用
    *   **内容:**
        *   K-mer 定义与原理。
        *   K-mer 频数分布曲线解读。
        *   利用K-mer估算基因组大小、杂合度、重复序列比例。
    *   **对应目标:** 1.2
*   **下午 (实践)**
    *   **主题:** K-mer 分析实战
    *   **工具:** Jellyfish / KMC
    *   **内容:**
        *   使用Jellyfish/KMC对二代测序数据进行K-mer计数。
        *   生成K-mer频数分布表。
        *   (可选) 使用 GCE 或 GenomeScope 等工具进行基因组特征估算。
    *   **对应目标:** 1.2, 1.3
    *   **实践:** 运行K-mer计数，尝试估算基因组特征。

**Day 4: 基因组组装理论与准备**
*   **上午 (理论)**
    *   **主题:** 基因组组装策略与三代测序数据
    *   **内容:**
        *   基因组组装的基本概念 (Contig, Scaffold, Gap)。
        *   主要组装策略: OLC (Overlap-Layout-Consensus), De Bruijn Graph。
        *   三代测序 (PacBio HiFi, ONT) 数据特点 (长读长、错误率模型)。
        *   适用于三代数据的组装软件介绍 (Flye, Canu, Hifiasm等)。
        *   组装中的挑战 (重复序列、高杂合区域)。
    *   **对应目标:** 2.1, 3.1, 3.3, 3.2
*   **下午 (实践准备)**
    *   **主题:** 组装实战准备
    *   **内容:**
        *   下载或准备用于组装的三代测序数据 (示例数据)。
        *   检查数据完整性。
        *   安装基因组组装软件 (e.g., Flye) 到Conda环境。
        *   准备组装提交脚本。
    *   **对应目标:** 1.3

**Day 5: 基因组组装实战**
*   **全天 (实践 + 监控)**
    *   **主题:** 运行基因组组装
    *   **内容:**
        *   使用选定的组装软件 (e.g., Flye) 提交组装任务到HPC。
        *   监控作业运行状态 (qstat)。
        *   查看日志文件，初步排查错误。
        *   (根据数据量和计算资源，组装可能需要较长时间)
    *   **对应目标:** 2.1, 1.3
    *   **实践:** 提交组装任务，学习监控和管理HPC作业。

---

## 第二周

**总体目标:** 掌握基因组注释、评价方法，并进行物种鉴定和系统发育分析。

**Day 6: 组装后处理与基因组注释**
*   **上午 (理论 + 检查)**
    *   **主题:** 组装结果检查与注释理论
    *   **内容:**
        *   检查组装任务是否完成，查看主要输出文件 (assembly.fasta)。
        *   组装基本统计指标 (N50, L50, 总长度等) - 概念。
        *   (可选) 组装后Polish的概念 (使用二代数据或原始三代数据校正)。
        *   基因组注释的目标和流程。
        *   细菌/古菌基因组注释常用工具 (Prokka)。
    *   **对应目标:** 2.1, 2.2, 3.1, 3.2
*   **下午 (实践)**
    *   **主题:** 细菌基因组注释实战
    *   **工具:** Prokka
    *   **内容:**
        *   安装 Prokka 及其依赖。
        *   使用 Prokka 对组装好的基因组进行注释。
        *   理解 Prokka 的主要输出文件 (gff, ffn, faa, fna, tsv)。
    *   **对应目标:** 2.2, 1.3
    *   **实践:** 运行Prokka注释，浏览输出结果。

**Day 7: 基因组质量评价**
*   **上午 (理论)**
    *   **主题:** 基因组组装与注释质量评价
    *   **内容:**
        *   评价指标:
            *   组装连续性 (N50/L50, 最大Contig等)。
            *   完整性 (BUSCO, CheckM - 基于保守单拷贝基因)。
            *   准确性 (与参考基因组比对 - Quast; Polish前后的比较)。
            *   污染评估 (CheckM)。
        *   常用评价工具介绍 (Quast, BUSCO, CheckM)。
    *   **对应目标:** 1.2, 2.3
*   **下午 (实践)**
    *   **主题:** 基因组评价实战
    *   **工具:** Quast, CheckM
    *   **内容:**
        *   安装 Quast 和 CheckM。
        *   使用 Quast 评估基因组组装统计数据 (可包含与参考基因组的比对)。
        *   使用 CheckM 评估基因组的完整性和污染度。
        *   解读 Quast 和 CheckM 的报告。
    *   **对应目标:** 2.3, 1.3
    *   **实践:** 运行Quast和CheckM，分析结果报告。

**Day 8: 物种鉴定**
*   **上午 (理论)**
    *   **主题:** 基于基因组的物种鉴定
    *   **内容:**
        *   物种鉴定的挑战与方法。
        *   平均核苷酸一致性 (Average Nucleotide Identity, ANI) 的概念和原理。
        *   ANI 的计算方法和常用工具 (JSpeciesWS - Web, FastANI - 命令行)。
        *   ANI 在细菌/古菌物种界定中的阈值。
    *   **对应目标:** 3.3
*   **下午 (实践)**
    *   **主题:** ANI 计算实战
    *   **工具:** FastANI (命令行)
    *   **内容:**
        *   安装 FastANI。
        *   准备查询基因组和参考基因组列表。
        *   运行 FastANI 计算两两基因组间的 ANI 值。
        *   解读输出结果，判断物种归属。
    *   **对应目标:** 3.3, 1.3
    *   **实践:** 下载参考基因组，运行FastANI进行物种鉴定。

**Day 9: 系统发育分析**
*   **上午 (理论 + 实践准备)**
    *   **主题:** 基于全基因组的系统发育树构建
    *   **内容:**
        *   系统发育分析基本概念。
        *   基于标记基因 vs 基于全基因组的系统发育分析。
        *   常用工具介绍:
            *   NCBI Datasets: 命令行下载基因组数据。
            *   GToTree: 基于单拷贝基因构建系统发育树。
            *   Mashtree: 基于 Mash distance 快速构建近似系统发育树。
    *   **对应目标:** 3.1, 3.2
    *   **实践准备:** 安装 NCBI Datasets, GToTree, Mashtree。
*   **下午 (实践)**
    *   **主题:** 系统发育树构建实战
    *   **工具:** NCBI Datasets, GToTree / Mashtree
    *   **内容:**
        *   使用 `datasets` 下载相关物种的基因组。
        *   使用 GToTree (推荐) 或 Mashtree 构建系统发育树。
        *   (可选) 使用 FigTree 或 iTOL 可视化树文件。
    *   **对应目标:** 3.1, 3.2, 1.3
    *   **实践:** 下载数据，运行GToTree/Mashtree，尝试可视化结果。

**Day 10: 总结与报告撰写**
*   **上午 (复习 + Q&A)**
    *   **主题:** 课程回顾与答疑
    *   **内容:**
        *   梳理整个分析流程。
        *   回顾关键概念和工具。
        *   解答学生在实践中遇到的问题。
*   **下午 (指导 + 实践)**
    *   **主题:** 实践报告撰写指导与实践
    *   **内容:**
        *   讲解实践报告的要求 (格式、内容、深度)。
        *   强调记录命令、参数选择依据、结果解释的重要性。
        *   学生开始整理笔记，撰写报告。
        *   教师提供个别指导。
    *   **对应目标:** 1.2, 2.1, 2.2, 2.3, 3.1, 3.2 (通过报告体现)

---
**教师:** 王运生, 张莹钧, 陈渊
**实践环境:** 校内HPC集群，学生个人电脑 (通过SSH访问集群)
**主要软件:** Conda, FastQC, Jellyfish/KMC, Flye/Canu, Prokka, Quast, CheckM, FastANI, NCBI Datasets, GToTree/Mashtree, (FigTree/iTOL)# 《高通量测序数据分析》实践指导手册 (Practical Guide)

**欢迎来到《高通量测序数据分析》实践课程！**

本手册旨在指导你完成课程中的各项实践操作。请仔细阅读每一步说明，并在你的终端（连接到HPC集群）中执行相应的命令。

**重要提示:**
*   所有命令均假设在Linux环境下执行。
*   `$` 或 `#` 开头的行表示命令行提示符，后面的内容是需要输入的命令。不要输入提示符本身。
*   `<placeholder>` 表示你需要替换成实际值的内容 (例如, `<your_username>`, `<path_to_data>`)。
*   务必记录你运行的每一个命令、使用的参数以及参数选择的理由。这对撰写最终的实践报告至关重要。
*   部分命令可能需要较长时间运行，请耐心等待或使用作业调度系统提交到后台运行。
*   示例数据路径和文件名需要根据实际情况修改。

---

## 实践一: HPC集群入门与环境准备

### 1.1 登录HPC集群

使用SSH客户端 (如终端、PuTTY、MobaXterm) 登录到指定的HPC集群。

```bash
ssh <your_username>@<cluster_address>
# 输入你的密码
```

### 1.2 Linux 基础命令回顾

练习以下常用命令：
*   `pwd`: 显示当前工作目录。
*   `ls -lht`: 查看当前目录内容 (详细列表、人类可读大小、按时间排序)。
*   `mkdir <new_directory_name>`: 创建新目录 (例如: `mkdir ngs_analysis`)。
*   `cd <directory_name>`: 切换目录 (例如: `cd ngs_analysis`)。
*   `cp <source_file> <destination>`: 复制文件。
*   `mv <source> <destination>`: 移动或重命名文件/目录。
*   `rm <file_name>`: 删除文件 (谨慎使用!)。
*   `rm -r <directory_name>`: 删除目录及其内容 (非常谨慎!)。
*   `head <file_name>`: 查看文件开头几行。
*   `tail <file_name>`: 查看文件末尾几行。
*   `less <file_name>`: 分页查看文件内容 (按 `q` 退出)。
*   `grep <pattern> <file_name>`: 在文件中搜索模式。

### 1.3 作业调度系统 (以SGE为例)

*   **查看队列状态:** `qstat`
*   **查看节点负载:** `qhost`
*   **提交作业:**
    *   创建一个脚本文件 (例如 `myjob.sh`):
      ```bash
      #!/bin/bash
      #$ -N my_test_job  # 作业名称
      #$ -cwd           # 在当前工作目录运行
      #$ -q <queue_name> # 指定队列 (询问管理员)
      #$ -pe <parallel_environment> <num_cores> # 请求并行环境和核心数 (如果需要)
      #$ -l vf=<memory_amount>G,p=<priority> # 请求内存和其他资源

      echo "Job started on $(hostname) at $(date)"
      # 在这里写下你的命令, 例如:
      sleep 60
      echo "Job finished at $(date)"
      ```
    *   提交脚本: `qsub myjob.sh`
*   **查看作业详情:** `qstat -j <job_id>`
*   **删除作业:** `qdel <job_id>`

### 1.4 Conda 环境管理

*   **安装 Miniconda (如果尚未安装):**
    *   下载安装脚本: `wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh`
    *   运行安装脚本: `bash Miniconda3-latest-Linux-x86_64.sh` (按照提示操作，建议接受默认设置，并在最后允许 `conda init`)
    *   关闭并重新打开终端，或运行 `source ~/.bashrc` 使配置生效。
*   **配置 Channels (推荐):**
    ```bash
    conda config --add channels defaults
    conda config --add channels bioconda
    conda config --add channels conda-forge
    conda config --set channel_priority strict
    ```
*   **创建课程环境:**
    ```bash
    conda create -n ngs_course python=3.9 -y
    ```
*   **激活环境:**
    ```bash
    conda activate ngs_course
    ```
    *(之后的所有软件安装和运行都在此环境下进行)*
*   **安装基础工具 (示例):**
    ```bash
    conda install -c bioconda fastqc -y
    ```
*   **查看已安装的包:** `conda list`
*   **退出环境:** `conda deactivate`

---

## 实践二: NGS数据质控 (FastQC)

*   **激活环境:** `conda activate ngs_course`
*   **获取数据:** 假设你的原始测序数据 (FASTQ格式) 位于 `<data_directory>`，文件名为 `reads_1.fq.gz` 和 `reads_2.fq.gz` (如果是双端测序)。
*   **运行 FastQC:**
    ```bash
    # 创建输出目录
    mkdir fastqc_results

    # 对每个文件运行FastQC
    fastqc <data_directory>/reads_1.fq.gz -o fastqc_results/
    fastqc <data_directory>/reads_2.fq.gz -o fastqc_results/
    # 如果文件很多，可以使用通配符: fastqc <data_directory>/*.fq.gz -o fastqc_results/
    ```
*   **查看报告:**
    *   FastQC 会在 `fastqc_results/` 目录下生成 HTML 报告文件。
    *   将这些 HTML 文件下载到你的本地电脑 (使用 `scp` 或 SFTP 客户端)。
    *   用浏览器打开 HTML 文件，仔细查看各项指标 (Per base sequence quality, Per sequence quality scores, Per base sequence content, GC content, Adapter Content 等)。
    *   根据报告判断数据质量，是否存在接头污染等问题。*(注意：实际分析中可能需要根据QC结果进行数据过滤，如使用Trimmomatic或fastp，本实践暂不涉及)*

---

## 实践三: 基因组Survey (K-mer分析)

*   **激活环境:** `conda activate ngs_course`
*   **安装 K-mer 计数工具 (以 Jellyfish 为例):**
    ```bash
    conda install -c bioconda jellyfish -y
    ```
*   **获取数据:** 使用二代测序数据 (通常是 Illumina 数据)，假设为 `reads_1.fq.gz` 和 `reads_2.fq.gz`。
*   **运行 Jellyfish 计数:**
    ```bash
    # 选择合适的 K 值 (通常 17, 19, 21, ... 31 之间)
    K=21

    # 计数 (假设是gzip压缩文件)
    # -m: K值
    # -s: hash大小 (根据基因组大小和K值估算，例如 1G, 5G)
    # -t: 线程数
    # -C: 处理双端文件
    # --gzip: 输入是gzip压缩
    jellyfish count -m $K -s 5G -t 8 -C --gzip -o kmer_counts.jf <data_directory>/reads_*.fq.gz

    # 生成频数分布直方图数据
    jellyfish histo -t 8 kmer_counts.jf > kmer_histo.tsv
    ```
*   **解读结果:**
    *   查看 `kmer_histo.tsv` 文件。第一列是 K-mer 出现次数 (频数)，第二列是具有该频数的 K-mer 种类数。
    *   绘制 K-mer 频数分布图 (可以使用 R 或 Python)。
    *   观察主峰位置 (对应杂合 K-mer 深度)，估算基因组大小 (总 K-mer 数 / 主峰深度)。
    *   观察是否有重复序列峰、高杂合峰等。
    *   (可选) 使用 GenomeScope (在线或本地) 分析 `kmer_histo.tsv` 文件，获取更详细的基因组特征估计 (大小、杂合度、重复比例)。

---

## 实践四: 基因组组装 (以 Flye 为例)

*   **激活环境:** `conda activate ngs_course`
*   **安装 Flye:**
    ```bash
    conda install -c bioconda flye -y
    ```
*   **获取数据:** 使用三代测序数据 (PacBio HiFi 或 ONT)，假设文件为 `long_reads.fq.gz`。
*   **准备组装脚本 (例如 `flye_job.sh`):**
    ```bash
    #!/bin/bash
    #$ -N flye_assembly
    #$ -cwd
    #$ -q <queue_name>
    #$ -pe <parallel_environment> 16 # 请求16个核心
    #$ -l vf=64G,p=1 # 请求64G内存

    conda activate ngs_course # 确保在正确的环境中运行

    # 输入的长读长文件
    READS="<path_to_data>/long_reads.fq.gz"
    # 输出目录
    OUT_DIR="flye_output"
    # 线程数 (与请求的核心数匹配)
    THREADS=16
    # 预估基因组大小 (可选但推荐, 例如 5m 表示 5Mb)
    GENOME_SIZE="5m"
    # 测序数据类型: --pacbio-raw, --pacbio-hifi, --nano-raw, --nano-hq
    READ_TYPE="--pacbio-hifi" # 根据你的数据类型修改

    echo "Starting Flye assembly at $(date)"

    flye ${READ_TYPE} ${READS} \
         --out-dir ${OUT_DIR} \
         --genome-size ${GENOME_SIZE} \
         --threads ${THREADS}

    echo "Flye assembly finished at $(date)"
    ```
*   **提交组装任务:**
    ```bash
    qsub flye_job.sh
    ```
*   **监控与结果:**
    *   使用 `qstat` 监控作业状态。
    *   组装完成后，进入 `flye_output` 目录。
    *   主要结果文件是 `assembly.fasta` (组装得到的 Contigs)。
    *   查看 `flye.log` 文件获取详细运行信息和统计数据。
    *   初步评估组装结果: 查看 `assembly_info.txt` 文件中的统计信息 (总长度, Contig 数量, N50 等)。

---

## 实践五: 基因组注释 (Prokka)

*   **激活环境:** `conda activate ngs_course`
*   **安装 Prokka:**
    ```bash
    conda install -c bioconda prokka -y
    # Prokka 依赖较多，首次安装可能需要较长时间
    # 可能需要安装一些数据库，根据 Prokka 提示操作
    # prokka --setupdb
    ```
*   **获取数据:** 使用上一步组装得到的 `assembly.fasta` 文件。
*   **运行 Prokka:**
    ```bash
    # 输入的组装文件
    ASSEMBLY="flye_output/assembly.fasta"
    # 输出目录
    OUT_DIR="prokka_annotation"
    # 物种属名 (可选, 提高注释准确性)
    GENUS="GenusName" # 替换成你的目标属名
    # 物种名 (可选)
    SPECIES="speciesname" # 替换成你的目标种名
    # 菌株名 (可选)
    STRAIN="strain123"
    # 指定遗传密码子表 (细菌通常是 11)
    KINGDOM="Bacteria" # 或 Archaea, Viruses
    GCODE=11
    # 线程数
    THREADS=8

    prokka --outdir ${OUT_DIR} --prefix ${STRAIN} \
           --genus ${GENUS} --species ${SPECIES} --strain ${STRAIN} \
           --kingdom ${KINGDOM} --gcode ${GCODE} \
           --cpus ${THREADS} \
           ${ASSEMBLY}

    echo "Prokka annotation finished at $(date)"
    ```
    *(如果是在HPC上运行，建议写成提交脚本)*
*   **解读结果:**
    *   进入 `prokka_annotation` 目录。
    *   `.gff`: GFF3 格式的注释文件，包含基因位置、功能等信息。
    *   `.fna`: 注释后的基因组序列 (FASTA)。
    *   `.ffn`: 核酸序列 (FASTA)，包含所有预测出的 CDS 和 RNA。
    *   `.faa`: 氨基酸序列 (FASTA)，包含所有预测出的蛋白质。
    *   `.tsv`: 制表符分隔的注释摘要信息。
    *   `.txt`: 注释运行的统计报告。
    *   浏览这些文件，了解基因组包含哪些基因、功能等。

---

## 实践六: 基因组质量评价 (Quast & CheckM)

*   **激活环境:** `conda activate ngs_course`
*   **安装 Quast 和 CheckM:**
    ```bash
    conda install -c bioconda quast checkm-genome -y
    # CheckM 可能需要下载数据库，根据提示操作
    # checkm data update (可能需要较长时间和空间)
    ```
*   **获取数据:**
    *   组装结果: `flye_output/assembly.fasta`
    *   (可选) 参考基因组: `<reference_genome.fasta>`
    *   (可选) 注释文件: `prokka_annotation/*.gff`

### 6.1 Quast 评估组装统计

```bash
# 输入的组装文件
ASSEMBLY="flye_output/assembly.fasta"
# 输出目录
OUT_DIR="quast_results"
# 线程数
THREADS=8
# (可选) 参考基因组
REF_GENOME="<path_to_reference>/reference.fasta"
# (可选) 注释文件 (GFF格式)
GFF_FILE="prokka_annotation/*.gff" # 使用Prokka的GFF

# 运行 Quast (带参考基因组和注释)
quast.py ${ASSEMBLY} \
         -o ${OUT_DIR} \
         -r ${REF_GENOME} \
         -g ${GFF_FILE} \
         --threads ${THREADS}

# 运行 Quast (不带参考基因组)
# quast.py ${ASSEMBLY} -o ${OUT_DIR} --threads ${THREADS}
```
*   **解读结果:**
    *   进入 `quast_results` 目录。
    *   查看 `report.html` (下载到本地用浏览器打开) 或 `report.tsv`。
    *   关注 N50, L50, 总长度, 最大Contig长度, Contig数量等组装连续性指标。
    *   如果提供了参考基因组，关注比对相关的指标 (Genome fraction %, Mismatches per 100 kbp, Indels per 100 kbp) 和 Misassemblies。
    *   如果提供了GFF，关注找到的基因数量。

### 6.2 CheckM 评估完整性和污染度

```bash
# 输入的组装文件所在的目录 (CheckM需要目录作为输入)
ASSEMBLY_DIR="flye_output/" # 假设 assembly.fasta 在此目录下
# 输出目录
OUT_DIR="checkm_results"
# 线程数
THREADS=8

# 运行 CheckM (lineage_wf 工作流)
checkm lineage_wf ${ASSEMBLY_DIR} ${OUT_DIR} \
       --tab_table \
       -t ${THREADS} \
       -x fasta # 指定输入文件扩展名

# (如果知道物种分类，可以指定更具体的标记基因集，例如:)
# checkm taxon_list # 查看可用分类单元
# checkm lineage_wf -t 16 -x fa --reduced_tree --tab_table -f checkm.tsv Bacteria prokka_annotation/ checkm_output
```
*   **解读结果:**
    *   进入 `checkm_results` 目录。
    *   查看 `lineage.ms` 文件 (或用 `--tab_table` 生成的 TSV 文件)。
    *   关注 **Completeness** (完整性) 和 **Contamination** (污染度)。
    *   理想的基因组应该具有高完整性 (接近 100%) 和低污染度 (接近 0%)。
    *   Strain heterogeneity (菌株异质性) 指标也值得关注。

---

## 实践七: 基于全基因组的物种鉴定 (FastANI)

*   **激活环境:** `conda activate ngs_course`
*   **安装 FastANI:**
    ```bash
    conda install -c bioconda fastani -y
    ```
*   **获取数据:**
    *   你的查询基因组: `flye_output/assembly.fasta`
    *   参考基因组: 从 NCBI 或其他数据库下载已知物种的基因组 FASTA 文件。将它们放在一个目录下，例如 `reference_genomes/`。
    *   创建一个包含所有参考基因组文件路径的列表文件 `ref_list.txt` (每行一个文件路径)。
      ```bash
      # 例如，在 reference_genomes/ 目录下运行:
      find $(pwd)/reference_genomes -name "*.fasta" > ref_list.txt
      # 或者手动创建:
      # /path/to/reference_genomes/speciesA.fasta
      # /path/to/reference_genomes/speciesB.fasta
      # ...
      ```
*   **运行 FastANI:**
    ```bash
    # 查询基因组
    QUERY_GENOME="flye_output/assembly.fasta"
    # 参考基因组列表
    REF_LIST="ref_list.txt"
    # 输出文件
    OUTPUT_FILE="ani_results.txt"
    # 线程数
    THREADS=8

    fastani --query ${QUERY_GENOME} --refList ${REF_LIST} -o ${OUTPUT_FILE} -t ${THREADS}
    ```
*   **解读结果:**
    *   查看 `ani_results.txt` 文件。
    *   文件包含五列: 查询基因组, 参考基因组, ANI值 (%), 比对上的片段数, 总片段数。
    *   找到 ANI 值最高的参考基因组。
    *   通常，ANI > 95-96% 被认为是同种的标准。根据这个阈值判断你的组装基因组属于哪个物种。

---

## 实践八: 基于全基因组构建系统进化树

*   **激活环境:** `conda activate ngs_course`
*   **安装工具:**
    ```bash
    # NCBI Datasets 命令行工具
    # (安装方法可能随版本变化，请参考官方文档)
    # 通常需要下载二进制文件或使用包管理器
    # 例如:
    # wget <link_to_datasets_binary>
    # chmod +x datasets
    # ./datasets version # 检查安装

    # GToTree
    conda install -c bioconda gtotree -y

    # Mashtree (可选, 快速方法)
    conda install -c bioconda mashtree -y

    # 可视化工具 (可选, 本地安装)
    # FigTree, iTOL (在线)
    ```

### 8.1 使用 NCBI Datasets 下载基因组

*   确定你要分析的物种范围 (例如，与你的目标物种相关的几个近缘种)。
*   查找这些物种的 NCBI Assembly Accession 号 (例如 GCF_xxxxxxxx.x)。
*   创建一个包含这些 Accession 号的列表文件 `accessions.txt` (每行一个)。
*   下载基因组:
    ```bash
    datasets download genome accession --inputfile accessions.txt --include genome --filename genomes.zip
    unzip genomes.zip -d downloaded_genomes
    # 整理下载的文件，通常在 downloaded_genomes/ncbi_dataset/data/ 目录下找到 FASTA 文件 (.fna)
    # 将你的组装基因组 (assembly.fasta) 也复制到包含下载基因组的目录中
    ```

### 8.2 使用 GToTree 构建系统发育树 (基于单拷贝基因)

```bash
# 包含所有基因组 FASTA 文件的目录
GENOMES_DIR="path/to/all/genomes/" # 包含你的组装和下载的基因组
# 选择一个合适的单拷贝基因集 (HMM模型)
# 例如，细菌: Bacteria.hmm (GToTree自带)
HMM_SET="Bacteria.hmm"
# 输出文件前缀
OUT_PREFIX="phylogeny"
# 线程数
THREADS=16

# 运行 GToTree
GToTree -A ${GENOMES_DIR}/*.fna -H ${HMM_SET} -o ${OUT_PREFIX} -j ${THREADS}
# GToTree 会自动完成基因查找、比对、过滤、建树等步骤
```
*   **解读结果:**
    *   主要输出文件是 `${OUT_PREFIX}.tre` (Newick 格式的树文件)。
    *   将此文件下载到本地，使用 FigTree 或 iTOL 等软件进行可视化和美化。
    *   观察你的基因组在进化树上的位置，确定其与其他物种的亲缘关系。

### 8.3 (可选) 使用 Mashtree 快速构建系统发育树

```bash
# 包含所有基因组 FASTA 文件的目录
GENOMES_DIR="path/to/all/genomes/"
# 输出树文件
OUT_TREE="mashtree_output.tre"
# 线程数
THREADS=16

mashtree --num-threads ${THREADS} ${GENOMES_DIR}/*.fna > ${OUT_TREE}
```
*   **解读结果:**
    *   同样得到 Newick 格式的树文件 `${OUT_TREE}`。
    *   Mashtree 基于 Mash distance，速度快，但可能不如基于多基因比对的方法精确，适用于快速预览。

---

## 实践九: 实践报告撰写

*   **整理笔记:** 回顾你在每个实践步骤中运行的命令、使用的参数、遇到的问题及解决方法。
*   **报告结构:**
    *   **封面:** 课程名称、姓名、学号、日期等。
    *   **引言:** 简述高通量测序数据分析的重要性及本次实践的目标。
    *   **方法:**
        *   详细描述每个分析步骤 (QC, Survey, Assembly, Annotation, Evaluation, Identification, Phylogeny)。
        *   列出使用的主要软件及其版本。
        *   **关键:** 清晰记录每一步运行的核心命令和所使用的参数。解释为什么选择这些参数 (例如 K-mer 值的选择、Flye 的数据类型参数、Prokka 的遗传密码子等)。
    *   **结果与讨论:**
        *   展示每个步骤的关键结果 (例如 FastQC 报告截图、K-mer 分布图、组装统计表、注释基因数量、Quast/CheckM 评估结果、ANI值、系统发育树图)。
        *   对结果进行必要的描述、说明和解释。例如：
            *   数据质量如何？是否需要过滤？
            *   基因组 Survey 揭示了哪些基因组特征？
            *   组装结果的连续性和完整性如何？
            *   注释发现了哪些重要的基因或通路？
            *   基因组评价结果是否达到标准？
            *   你的样本被鉴定为哪个物种？依据是什么？
            *   系统发育树揭示了怎样的进化关系？
        *   讨论分析过程中遇到的问题以及如何解决的。
        *   (可选) 对比不同参数或软件可能产生的影响。
    *   **结论:** 总结本次实践的主要发现和学习收获。
    *   **参考文献:** (如果引用了外部资料)
*   **格式要求:** 报告清晰、格式规范、图表清晰。
