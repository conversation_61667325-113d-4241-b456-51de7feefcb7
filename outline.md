**《高通量测序数据分析》教学大纲**

**课程英文名称：**Data Analysis of NGS

**课程代码：**B392J03000

**课程学时：**2.00（理论0.00学时，实践0.00学时，实习（实践）2.00周）

**课程学分：**2

**课程分类：** 公共必修课 专业必修课 专业选修课

> 集中性实践 公共选修课

**是否专业核心课程：** 是 否

**开课单位：**植物保护学院

**面向专业：**生物信息学

**先修课程：**基因组学，生物信息学，R语言编程，Linux基础与应用

**课程负责人：**王运生

**课程教学团队：**张莹钧、陈渊

**建议使用教材：**

**主要参考书目和阅读材料：**

1.转录组分析基础与流程：https://scienceparkstudygroup.github.io/rna-seq-lesson/index.html

2.Data Analysis in Genome
Biology：https://girke.bioinformatics.ucr.edu/GEN242/index.html

3.泛基因组：https://evomics.org/2023-workshop-on-genomics-cesky-krumlov/

1.  **课程简介**

    随着新一代高通量测序技术的快速发展，测序成本进一步降低，高通量测序在生命科学研究中应用越来越广。由此不断产生的测序数据有着数量巨大、关系复杂的特点，往往需借助于专门服务器才能实现数据的存储和分析。生命科学研究的技术手段和模式飞速发展，基因组、蛋白质组、转录组、代谢组和相互作用组等各种组学的研究向着更加微观深入，各大测序平台产生的海量复杂的多组学数据亟需运用生物信息学的方法进行存储、分析和发布，生物信息学研究重点也随之从单基因分析转移到组学大数据的剖析上，对从事生命科学研究的科技人员也提出了新的更高要求。本课程为实践课程，通过设计一系列的高通量测序数据分析专题，用实际项目数据为基础，让学生熟悉并掌握高通量测序数据分析的主要特征和基本流程。

**二、教学理念**

用理论指导实践，先了解理论，然后在实践过程中再加深对理论的理解。但同时需加强上机操作训练，通过实际的测序数据进行上机实操，加深对数据分析的感性认识。

**三、课程目标**

<table>
<colgroup>
<col style="width: 61%" />
<col style="width: 25%" />
<col style="width: 12%" />
</colgroup>
<tbody>
<tr class="odd">
<td><strong>课程目标</strong></td>
<td colspan="2"><strong>对应的专业毕业要求</strong></td>
</tr>
<tr class="even">
<td><ol type="1">
<li><p><strong>知识层面：</strong></p>
<p>1.1了解DNA测序技术发展历史</p>
<p>1.2了解高通量测序分析的常见数据格式及概念，如N50, conntig,
scaffold等</p>
<p>1.3熟悉高性能计算集群使用</p></li>
</ol></td>
<td>生物信息学：毕业要求4</td>
<td>0.2</td>
</tr>
<tr class="odd">
<td><ol start="2" type="1">
<li><p><strong>能力层面：</strong></p>
<p>2.1掌握基于三代测序数据基因组组装流程</p>
<p>2.2掌握细菌基因组注释方法</p>
<p>2.3掌握基因组评价方法</p></li>
</ol></td>
<td>生物信息学：毕业要求5</td>
<td>0.6</td>
</tr>
<tr class="even">
<td><ol start="3" type="1">
<li><p><strong>素质层面：</strong></p>
<p>3.1理解基因组组装的算法</p>
<p>3.2理解基因组组装过程中对于重复序列、高杂区域的处理方法</p>
<p>3.3理解不同测序平台的应用场景</p></li>
</ol></td>
<td>生物信息学：毕业要求6</td>
<td>0.2</td>
</tr>
</tbody>
</table>

**四、教学内容、要求与课程目标关系表**

**实习（实践）**

<table>
<colgroup>
<col style="width: 27%" />
<col style="width: 27%" />
<col style="width: 8%" />
<col style="width: 9%" />
<col style="width: 26%" />
</colgroup>
<tbody>
<tr class="odd">
<td><p><strong>实习</strong></p>
<p><strong>（实践）</strong></p>
<p><strong>项目名称</strong></p></td>
<td><strong>教学要求</strong></td>
<td><strong>周数</strong></td>
<td><strong>实习类别</strong></td>
<td><p><strong>支撑的</strong></p>
<p><strong>课程目标</strong></p></td>
</tr>
<tr class="even">
<td>高通量测序技术发展历史</td>
<td><p>了解高通量测序技术发展历史；</p>
<p>了解高通量测序技术原理</p></td>
<td>0.1</td>
<td>专业实习</td>
<td>1.1</td>
</tr>
<tr class="odd">
<td>高性能计算机集群的使用及conda环境的搭建</td>
<td><p>掌握sge作业调度系统环境下提交任务的方法；</p>
<p>掌握conda安装及使用</p></td>
<td>0.1</td>
<td>专业实习</td>
<td>1.3</td>
</tr>
<tr class="even">
<td>基因组survey</td>
<td><p>熟悉k-mer的基本概念及分析方法；</p>
<p>掌握基于k-mer分析基因组的大小、重复序列情况及杂合度</p></td>
<td>0.2</td>
<td>专业实习</td>
<td>1.2</td>
</tr>
<tr class="odd">
<td>基因组组装</td>
<td>掌握基于三代测序数据，利用多个基因组组装软件进行基因组组装</td>
<td>0.4</td>
<td>专业实习</td>
<td>1.3,2.1</td>
</tr>
<tr class="even">
<td>基因组注释</td>
<td>掌握prokka使用方法</td>
<td>0.2</td>
<td>专业实习</td>
<td>2.2</td>
</tr>
<tr class="odd">
<td>基因组评价</td>
<td><p>熟悉基因组评价指标；</p>
<p>掌握Quast的使用;</p>
<p>掌握CheckM的使用;</p></td>
<td>0.2</td>
<td>专业实习</td>
<td>2.3</td>
</tr>
<tr class="even">
<td>基于全基因组的物种鉴定</td>
<td><p>熟悉ANI的概念及计算方法；</p>
<p>掌握JSpeciesWS的使用；</p></td>
<td>0.4</td>
<td>专业实习</td>
<td>3.3,2.1,2.2</td>
</tr>
<tr class="odd">
<td>基于全基因组构建系统进化树</td>
<td><p>掌握datasets的使用；</p>
<p>掌握mashtree和GToTree的使用</p></td>
<td>0.4</td>
<td>专业实习</td>
<td>3.1,3.2</td>
</tr>
</tbody>
</table>

5.  **教学方法**

    先集中在机房进行知识要点的讲解，然后对每个子项目的分析内容进行详细的解释。统一讲解后，学生根据实践指导上的要求和步骤进行上机操作，由于计算任务均在服务器上进行，学生实践操作可在学院机房进行，也可在学生自己的电脑上进行。大部分软件环境已经提前部署好，部分环境需要学生自己部署的会作出相应提示和要求。实践过程中，学生遇到任何问题可以通过在线方式进行解决。

6.  **课程考核**

    **（一）课程考核形式与要求**

|              |          |                                                                                                                              |                         |
|--------------|----------|------------------------------------------------------------------------------------------------------------------------------|-------------------------|
| **考核形式** | **分值** | **考核范围及要求**                                                                                                           | **对应课程目标**        |
| 实践报告     | 100      | 独立完成实验指导上的各项要求，详细记录每一步运行的命令及参数，选择不同参数时的依据，对每一步的结果进行必要的描述、说明和解释 | 1.2,2.1,2.2,2.3,3.1,3.2 |

**（二）课程目标评定标准**

<table>
<colgroup>
<col style="width: 23%" />
<col style="width: 15%" />
<col style="width: 14%" />
<col style="width: 15%" />
<col style="width: 10%" />
<col style="width: 11%" />
<col style="width: 10%" />
</colgroup>
<tbody>
<tr class="odd">
<td rowspan="2"><strong>课程目标</strong></td>
<td rowspan="2"><strong>考核方式</strong></td>
<td colspan="5"><strong>评价标准</strong></td>
</tr>
<tr class="even">
<td><p><strong>优秀</strong></p>
<p><strong>100-90分</strong></p></td>
<td><p><strong>良好</strong></p>
<p><strong>89-80分</strong></p></td>
<td><p><strong>中等</strong></p>
<p><strong>79-70分</strong></p></td>
<td><p><strong>一般</strong></p>
<p><strong>69-60分</strong></p></td>
<td><p><strong>不及格</strong></p>
<p><strong>&lt;60分</strong></p></td>
</tr>
<tr class="odd">
<td>课程目标1</td>
<td>考查</td>
<td>相关概念使用正确并能举一反三</td>
<td>概念使用正确，能区别相近概念</td>
<td>概念清晰，有必要说明</td>
<td>概念使用正确，但缺乏必要说明</td>
<td>概念不清晰</td>
</tr>
<tr class="even">
<td>课程目标2</td>
<td>考查</td>
<td>能独立完成各项任务、报告清晰，格式正确</td>
<td>能独立完成合项任务，报告撰写格式正确</td>
<td>能独立完成80%以上各项任务，报告格式正确</td>
<td>能独立完成50%以上任务，报告较清晰</td>
<td>不能独立完成任务，报告不清晰</td>
</tr>
<tr class="odd">
<td>课程目标3</td>
<td>考查</td>
<td>能理解各任务分析背后的算法及背景</td>
<td>能理解每一步的参数设置要求</td>
<td>能理解每一步分析的输入、输出要点</td>
<td>能理解每一步分析的结果</td>
<td>不能理解分析的生物学背景及算法，不知道如何设置参数</td>
</tr>
</tbody>
</table>

执笔人：王运生

审核人：李兰芝

审定成员：张莹钧、陈渊、王运生

审定时间：2023年09月18日
